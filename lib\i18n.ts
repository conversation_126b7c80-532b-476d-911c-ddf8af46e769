
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

const resources = {
  ar: {
    translation: {
      // Navigation
      'dashboard': 'لوحة التحكم',
      'profile': 'الملف الشخصي',
      'requests': 'الطلبات',
      'tasks': 'المهام',
      'reports': 'التقارير',
      'settings': 'الإعدادات',
      'logout': 'تسجيل الخروج',
      
      // Authentication
      'login': 'تسجيل الدخول',
      'register': 'إنشاء حساب',
      'email': 'البريد الإلكتروني',
      'password': 'كلمة المرور',
      'full_name': 'الاسم الكامل',
      'national_id': 'رقم الهوية الوطنية',
      'phone_number': 'رقم الهاتف',
      'login_tawtheeq': 'تسجيل الدخول عبر توثيق',
      'login_success': 'تم تسجيل الدخول بنجاح',
      'login_error': 'خطأ في البيانات المدخلة',

      // Dashboard Enhancements
      'welcome': 'مرحباً',
      'overview_status': 'نظرة عامة على حالة',
      'system_overview': 'النظام',
      'your_requests': 'طلباتك',
      'total_beneficiaries': 'إجمالي المستفيدين',
      'total_zakat_distributed': 'إجمالي الزكاة الموزعة',
      'pending_applications': 'الطلبات المعلقة',
      'active_distributions': 'التوزيعات النشطة',
      'quick_actions': 'الإجراءات السريعة',
      'register_new_beneficiary': 'تسجيل مستفيد جديد',
      'search_beneficiaries': 'البحث عن المستفيدين',
      'generate_report': 'إنشاء تقرير',
      'manage_distributions': 'إدارة التوزيعات',
      'recent_activity': 'النشاط الأخير',
      'distribution_overview': 'نظرة عامة على التوزيع',
      'monthly_distributions': 'التوزيعات الشهرية',
      'beneficiary_categories': 'فئات المستفيدين',
      'view_all': 'عرض الكل',
      'this_month': 'هذا الشهر',
      'last_month': 'الشهر الماضي',
      'last_week': 'الأسبوع الماضي',
      'this_week': 'هذا الأسبوع',
      'increase': 'زيادة',
      'decrease': 'انخفاض',
      'from': 'من',
      'sar': 'ريال سعودي',
      'beneficiaries': 'مستفيدين',
      'applications': 'طلبات',
      'distributions': 'توزيعات',
      'completed': 'مكتمل',
      'pending': 'معلق',
      'warning': 'تحذير',
      
      // Profile
      'personal_profile': 'الملف الشخصي',
      'basic_info': 'المعلومات الأساسية',
      'family_info': 'بيانات الأسرة',
      'employment_info': 'بيانات العمل',
      'financial_info': 'المعلومات المالية',
      'marital_status': 'الحالة الاجتماعية',
      'single': 'أعزب',
      'married': 'متزوج',
      'divorced': 'مطلق',
      'widowed': 'أرمل',
      
      // Assistance Requests
      'assistance_request': 'طلب المساعدة',
      'new_request': 'طلب جديد',
      'request_type': 'نوع المساعدة',
      'requested_amount': 'المبلغ المطلوب',
      'description': 'الوصف',
      'attach_documents': 'إرفاق المستندات',
      'submit_request': 'إرسال الطلب',
      
      // Status
      'draft': 'مسودة',
      'submitted': 'مرسل',
      
      // Workflow Stages
      'reception_review': 'مراجعة الاستقبال',
      'researcher_review': 'مراجعة الباحث',
      'banking_expert_review': 'مراجعة الخبير المصرفي',
      'department_head_review': 'مراجعة رئيس القسم',
      'admin_manager_review': 'مراجعة مدير الإدارة',
      'minister_review': 'مراجعة الوزير',
      
      // Actions
      'approve': 'موافقة',
      'reject': 'رفض',
      'return': 'إرجاع',
      'save': 'حفظ',
      'edit': 'تعديل',
      'delete': 'حذف',
      'view': 'عرض',
      'download': 'تحميل',
      'upload': 'رفع',
      
      // Common
      'loading': 'جاري التحميل...',
      'search': 'بحث',
      'filter': 'تصفية',
      'date': 'التاريخ',
      'amount': 'المبلغ',
      'status': 'الحالة',
      'notes': 'الملاحظات',
      'documents': 'المستندات',
      'history': 'التاريخ',
      
      // Dashboard Stats
      'total_requests': 'إجمالي الطلبات',
      'pending_review': 'في انتظار المراجعة',
      'approved_today': 'موافق عليها اليوم',
      'rejected_today': 'مرفوضة اليوم',
      'average_processing_days': 'متوسط أيام المعالجة',
      'total_users': 'إجمالي المستخدمين',
      
      // User Roles
      'zakat_applicant': 'مقدم طلب الزكاة',
      'reception_staff': 'موظف الاستقبال',
      'researcher': 'الباحث',
      'banking_expert': 'الخبير المصرفي',
      'department_head': 'رئيس القسم',
      'admin_manager': 'مدير الإدارة',
      'minister': 'الوزير',
      'system_admin': 'مسؤول النظام',

      // Beneficiary Management
      'beneficiary_management': 'إدارة المستفيدين',
      'beneficiary_management_desc': 'إدارة وتتبع المستفيدين من الزكاة والمساعدات',
      'beneficiary_registration': 'تسجيل مستفيد جديد',
      'beneficiary_registration_desc': 'إضافة مستفيد جديد إلى نظام إدارة الزكاة',
      'beneficiary_list': 'قائمة المستفيدين',
      'beneficiary_profile': 'الملف الشخصي للمستفيد',
      'beneficiary_details': 'تفاصيل المستفيد',
      'beneficiary_not_found': 'المستفيد غير موجود',
      'beneficiary_not_found_desc': 'لم يتم العثور على المستفيد المطلوب',
      'back_to_beneficiaries': 'العودة إلى قائمة المستفيدين',

      // Beneficiary Stats
      'approved_beneficiaries': 'المعتمدين',
      'under_review_beneficiaries': 'قيد المراجعة',
      'total_distributions': 'إجمالي التوزيعات',
      'average_distribution': 'متوسط التوزيع',
      'pending_verification_count': 'في انتظار التحقق',
      'needs_review': 'يحتاج إلى مراجعة',
      'of_total': 'من الإجمالي',
      'per_beneficiary': 'للمستفيد',

      // Search and Filters
      'search_and_filter': 'البحث والتصفية',
      'search_placeholder': 'البحث بالاسم، رقم الهوية، أو رقم الهاتف...',
      'filter_by_status': 'تصفية بالحالة',
      'filter_by_category': 'تصفية بالفئة',
      'all_statuses': 'جميع الحالات',
      'all_categories': 'جميع الفئات',
      'no_results_found': 'لا توجد نتائج مطابقة لمعايير البحث',
      'showing_results': 'عرض {{count}} من أصل {{total}} مستفيد',
      'export': 'تصدير',

      // Beneficiary Status
      'suspended': 'معلق',
      'inactive': 'غير نشط',

      // Zakat Categories
      'fuqara': 'الفقراء',
      'masakin': 'المساكين',
      'amilin': 'العاملين عليها',
      'muallafah': 'المؤلفة قلوبهم',
      'riqab': 'في الرقاب',
      'gharimin': 'الغارمين',
      'fisabilillah': 'في سبيل الله',
      'ibnus_sabil': 'ابن السبيل',
      'primary_category': 'الفئة الأساسية',

      // Table Headers
      'name': 'الاسم',
      'total_received': 'إجمالي المستلم',

      // Actions
      'view_profile': 'عرض الملف الشخصي',
      'manage_case': 'إدارة الحالة',
      'generate_voucher': 'إنشاء قسيمة',
      'back': 'العودة',

      // Profile Tabs
      'overview': 'نظرة عامة',
      'personal_details': 'البيانات الشخصية',
      'eligibility_verification': 'الأهلية والتحقق',
      'case_management': 'إدارة الحالة',
      'distribution_history': 'سجل التوزيعات',
      'family': 'الأسرة',

      // Profile Details
      'eligibility_score': 'نقاط الأهلية',
      'high_score': 'درجة عالية',
      'family_size': 'حجم الأسرة',
      'dependents': 'معالين',
      'last_distribution': 'آخر توزيع',
      'no_distribution': 'لا يوجد',
      'not_distributed_yet': 'لم يتم التوزيع بعد',

      // Contact Information
      'contact_info': 'معلومات الاتصال',
      'account_status': 'حالة الحساب',
      'current_status': 'الحالة الحالية',
      'registration_date': 'تاريخ التسجيل',
      'next_review': 'المراجعة القادمة',

      // Personal Information
      'personal_information': 'البيانات الشخصية',
      'basic_information': 'المعلومات الأساسية',
      'name_arabic': 'الاسم بالعربية',
      'name_english': 'الاسم بالإنجليزية',
      'date_of_birth': 'تاريخ الميلاد',
      'gender': 'الجنس',
      'male': 'ذكر',
      'female': 'أنثى',

      // Family Members
      'family_members': 'أفراد الأسرة',
      'no_family_info': 'لا توجد معلومات عن أفراد الأسرة',
      'dependent': 'معال',
      'special_needs': 'احتياجات خاصة',
      'relationship': 'القرابة',
      'age': 'العمر',
      'years': 'سنة',
      'son': 'ابن',
      'daughter': 'ابنة',
      'mother': 'أم',
      'father': 'أب',

      // Documents
      'no_documents': 'لا توجد مستندات مرفوعة',
      'verified': 'محقق',
      'pending_verification': 'في انتظار التحقق',
      'upload_date': 'تاريخ الرفع',

      // Coming Soon
      'coming_soon': 'قريباً',
      'under_development': 'قيد التطوير',
      'registration_form_coming': 'نموذج التسجيل قيد التطوير',
      'registration_form_desc': 'سيتم إضافة نموذج تسجيل المستفيدين الجدد قريباً',
      'will_include_features': 'سيتضمن النموذج الميزات التالية:',
      'multi_step_form': 'نموذج متعدد الخطوات مع مؤشر التقدم',
      'dual_language_input': 'إدخال البيانات الشخصية بالعربية والإنجليزية',
      'zakat_categories_selection': 'اختيار فئات الزكاة الثمانية',
      'document_upload': 'رفع المستندات المطلوبة',
      'data_validation': 'التحقق من صحة البيانات',
      'duplicate_detection': 'كشف التكرار التلقائي',

      // System Information
      'system_name': 'نظام إدارة الزكاة',
      'system_description': 'نظام شامل لإدارة طلبات الزكاة والمساعدات',

      // Authentication Messages
      'create_new_account': 'إنشاء حساب جديد',
      'sign_in_to_account': 'تسجيل الدخول إلى حسابك',
      'choose_login_method': 'اختر طريقة تسجيل الدخول المناسبة',
      'verifying': 'جاري التحقق...',
      'error': 'خطأ',
      'passwords_not_match': 'كلمات المرور غير متطابقة',
      'account_created_success': 'تم إنشاء الحساب بنجاح',
      'wait_admin_approval': 'يرجى انتظار موافقة الإدارة على حسابك',
      'account_creation_error': 'خطأ في إنشاء الحساب',
      'unexpected_error': 'حدث خطأ غير متوقع',
      'error_during_creation': 'حدث خطأ أثناء إنشاء الحساب',
      'invalid_credentials': 'البيانات المدخلة غير صحيحة',
      'welcome_to_system': 'مرحباً بك في نظام إدارة الزكاة',
      'error_during_login': 'حدث خطأ أثناء تسجيل الدخول',

      // Account Status
      'account_pending_approval': 'حسابك في انتظار الموافقة',
      'wait_admin_approval_desc': 'يرجى انتظار موافقة الإدارة على حسابك',

      // Dashboard
      'assigned_tasks': 'المهام المخصصة لك',
      'total_requests_desc': 'إجمالي الطلبات',
      'pending_review_desc': 'في انتظار المراجعة',
      'approved_today_desc': 'موافق عليها اليوم',
      'avg_processing_days_desc': 'متوسط أيام المعالجة',

      // Reports
      'no_reports_access': 'ليس لديك صلاحية للوصول إلى التقارير',
      'monthly_report': 'التقرير الشهري',
      'monthly_stats_desc': 'إحصائيات الطلبات والموافقات الشهرية',
      'requests_label': 'الطلبات',
      'approved_label': 'موافق',
      'rejected_label': 'مرفوض',

      // Requests
      'back_button': 'العودة',
      'request_details': 'تفاصيل الطلب',
      'download_decision': 'تحميل القرار',

      // Gender and Personal Info
      'gender_label': 'الجنس',
      'male_label': 'ذكر',
      'female_label': 'أنثى',
      'marital_status_label': 'الحالة الاجتماعية',
      'married_label': 'متزوج',
      'single_label': 'أعزب',
      'divorced_label': 'مطلق',
      'widowed_label': 'أرمل',

      // Common UI Text
      'or': 'أو',
      'no_account': 'ليس لديك حساب؟',
      'demo_accounts': 'حسابات تجريبية:',
      'applicant': 'مقدم طلب:',
      'staff_member': 'موظف:',

      // Access Control
      'access_denied': 'غير مصرح',
      'no_beneficiary_access': 'ليس لديك صلاحية للوصول إلى إدارة المستفيدين',
      'no_registration_access': 'ليس لديك صلاحية لتسجيل مستفيدين جدد',
    }
  },
  en: {
    translation: {
      // Navigation
      'dashboard': 'Dashboard',
      'profile': 'Profile',
      'requests': 'Requests',
      'tasks': 'Tasks',
      'reports': 'Reports',
      'settings': 'Settings',
      'logout': 'Logout',
      
      // Authentication
      'login': 'Login',
      'register': 'Register',
      'email': 'Email',
      'password': 'Password',
      'full_name': 'Full Name',
      'national_id': 'National ID',
      'phone_number': 'Phone Number',
      'login_tawtheeq': 'Login with Tawtheeq',
      'login_success': 'Login successful',
      'login_error': 'Invalid credentials',

      // Dashboard Enhancements
      'welcome': 'Welcome',
      'overview_status': 'Overview of',
      'system_overview': 'system status',
      'your_requests': 'your requests',
      'total_beneficiaries': 'Total Beneficiaries',
      'total_zakat_distributed': 'Total Zakat Distributed',
      'pending_applications': 'Pending Applications',
      'active_distributions': 'Active Distributions',
      'quick_actions': 'Quick Actions',
      'register_new_beneficiary': 'Register New Beneficiary',
      'search_beneficiaries': 'Search Beneficiaries',
      'generate_report': 'Generate Report',
      'manage_distributions': 'Manage Distributions',
      'recent_activity': 'Recent Activity',
      'distribution_overview': 'Distribution Overview',
      'monthly_distributions': 'Monthly Distributions',
      'beneficiary_categories': 'Beneficiary Categories',
      'view_all': 'View All',
      'this_month': 'This Month',
      'last_month': 'Last Month',
      'last_week': 'Last Week',
      'this_week': 'This Week',
      'increase': 'increase',
      'decrease': 'decrease',
      'from': 'from',
      'sar': 'SAR',
      'beneficiaries': 'beneficiaries',
      'applications': 'applications',
      'distributions': 'distributions',
      'completed': 'Completed',
      'pending': 'Pending',
      'warning': 'Warning',
      
      // Profile
      'personal_profile': 'Personal Profile',
      'basic_info': 'Basic Information',
      'family_info': 'Family Information',
      'employment_info': 'Employment Information',
      'financial_info': 'Financial Information',
      'marital_status': 'Marital Status',
      'single': 'Single',
      'married': 'Married',
      'divorced': 'Divorced',
      'widowed': 'Widowed',
      
      // Assistance Requests
      'assistance_request': 'Assistance Request',
      'new_request': 'New Request',
      'request_type': 'Request Type',
      'requested_amount': 'Requested Amount',
      'description': 'Description',
      'attach_documents': 'Attach Documents',
      'submit_request': 'Submit Request',
      
      // Status
      'draft': 'Draft',
      'submitted': 'Submitted',
      
      // Workflow Stages
      'reception_review': 'Reception Review',
      'researcher_review': 'Researcher Review',
      'banking_expert_review': 'Banking Expert Review',
      'department_head_review': 'Department Head Review',
      'admin_manager_review': 'Admin Manager Review',
      'minister_review': 'Minister Review',
      
      // Actions
      'approve': 'Approve',
      'reject': 'Reject',
      'return': 'Return',
      'save': 'Save',
      'edit': 'Edit',
      'delete': 'Delete',
      'view': 'View',
      'download': 'Download',
      'upload': 'Upload',
      
      // Common
      'loading': 'Loading...',
      'search': 'Search',
      'filter': 'Filter',
      'date': 'Date',
      'amount': 'Amount',
      'status': 'Status',
      'notes': 'Notes',
      'documents': 'Documents',
      'history': 'History',
      
      // Dashboard Stats
      'total_requests': 'Total Requests',
      'pending_review': 'Pending Review',
      'approved_today': 'Approved Today',
      'rejected_today': 'Rejected Today',
      'average_processing_days': 'Avg. Processing Days',
      'total_users': 'Total Users',
      
      // User Roles
      'zakat_applicant': 'Zakat Applicant',
      'reception_staff': 'Reception Staff',
      'researcher': 'Researcher',
      'banking_expert': 'Banking Expert',
      'department_head': 'Department Head',
      'admin_manager': 'Administration Manager',
      'minister': 'Minister',
      'system_admin': 'System Administrator',

      // Beneficiary Management
      'beneficiary_management': 'Beneficiary Management',
      'beneficiary_management_desc': 'Manage and track Zakat and assistance beneficiaries',
      'beneficiary_registration': 'New Beneficiary Registration',
      'beneficiary_registration_desc': 'Add a new beneficiary to the Zakat management system',
      'beneficiary_list': 'Beneficiary List',
      'beneficiary_profile': 'Beneficiary Profile',
      'beneficiary_details': 'Beneficiary Details',
      'beneficiary_not_found': 'Beneficiary Not Found',
      'beneficiary_not_found_desc': 'The requested beneficiary could not be found',
      'back_to_beneficiaries': 'Back to Beneficiaries List',

      // Beneficiary Stats
      'approved_beneficiaries': 'Approved',
      'under_review_beneficiaries': 'Under Review',
      'total_distributions': 'Total Distributions',
      'average_distribution': 'Average Distribution',
      'pending_verification_count': 'Pending Verification',
      'needs_review': 'Needs Review',
      'of_total': 'of Total',
      'per_beneficiary': 'per Beneficiary',

      // Search and Filters
      'search_and_filter': 'Search and Filter',
      'search_placeholder': 'Search by name, national ID, or phone number...',
      'filter_by_status': 'Filter by Status',
      'filter_by_category': 'Filter by Category',
      'all_statuses': 'All Statuses',
      'all_categories': 'All Categories',
      'no_results_found': 'No results found matching search criteria',
      'showing_results': 'Showing {{count}} of {{total}} beneficiaries',
      'export': 'Export',

      // Beneficiary Status
      'pending_verification': 'Pending Verification',
      'under_review': 'Under Review',
      'approved': 'Approved',
      'rejected': 'Rejected',
      'suspended': 'Suspended',
      'inactive': 'Inactive',

      // Zakat Categories
      'fuqara': 'The Poor',
      'masakin': 'The Needy',
      'amilin': 'Zakat Administrators',
      'muallafah': 'Those whose hearts are reconciled',
      'riqab': 'To free slaves/captives',
      'gharimin': 'Those in debt',
      'fisabilillah': 'In the cause of Allah',
      'ibnus_sabil': 'The wayfarer/traveler',
      'primary_category': 'Primary Category',

      // Table Headers
      'name': 'Name',
      'total_received': 'Total Received',

      // Actions
      'view_profile': 'View Profile',
      'manage_case': 'Manage Case',
      'generate_voucher': 'Generate Voucher',
      'back': 'Back',

      // Profile Tabs
      'overview': 'Overview',
      'personal_details': 'Personal Details',
      'eligibility_verification': 'Eligibility & Verification',
      'case_management': 'Case Management',
      'distribution_history': 'Distribution History',
      'family': 'Family',

      // Profile Details
      'eligibility_score': 'Eligibility Score',
      'high_score': 'High Score',
      'family_size': 'Family Size',
      'dependents': 'Dependents',
      'last_distribution': 'Last Distribution',
      'no_distribution': 'None',
      'not_distributed_yet': 'Not distributed yet',

      // Contact Information
      'contact_info': 'Contact Information',
      'account_status': 'Account Status',
      'current_status': 'Current Status',
      'registration_date': 'Registration Date',
      'next_review': 'Next Review',

      // Personal Information
      'personal_information': 'Personal Information',
      'basic_information': 'Basic Information',
      'name_arabic': 'Name in Arabic',
      'name_english': 'Name in English',
      'date_of_birth': 'Date of Birth',
      'gender': 'Gender',
      'male': 'Male',
      'female': 'Female',

      // Family Members
      'family_members': 'Family Members',
      'no_family_info': 'No family information available',
      'dependent': 'Dependent',
      'special_needs': 'Special Needs',
      'relationship': 'Relationship',
      'age': 'Age',
      'years': 'years',
      'son': 'Son',
      'daughter': 'Daughter',
      'mother': 'Mother',
      'father': 'Father',

      // Documents
      'no_documents': 'No documents uploaded',
      'verified': 'Verified',
      'upload_date': 'Upload Date',

      // Coming Soon
      'coming_soon': 'Coming Soon',
      'under_development': 'Under Development',
      'registration_form_coming': 'Registration Form Under Development',
      'registration_form_desc': 'New beneficiary registration form will be added soon',
      'will_include_features': 'The form will include the following features:',
      'multi_step_form': 'Multi-step form with progress indicator',
      'dual_language_input': 'Personal data input in Arabic and English',
      'zakat_categories_selection': 'Selection of eight Zakat categories',
      'document_upload': 'Required document upload',
      'data_validation': 'Data validation',
      'duplicate_detection': 'Automatic duplicate detection',

      // System Information
      'system_name': 'Zakat Management System',
      'system_description': 'Comprehensive system for managing Zakat and assistance requests',

      // Authentication Messages
      'create_new_account': 'Create new account',
      'sign_in_to_account': 'Sign in to your account',
      'choose_login_method': 'Choose the appropriate login method',
      'verifying': 'Verifying...',
      'error': 'Error',
      'passwords_not_match': 'Passwords do not match',
      'account_created_success': 'Account created successfully',
      'wait_admin_approval': 'Please wait for admin approval of your account',
      'account_creation_error': 'Error creating account',
      'unexpected_error': 'An unexpected error occurred',
      'error_during_creation': 'An error occurred during account creation',
      'invalid_credentials': 'Invalid credentials entered',
      'welcome_to_system': 'Welcome to the Zakat Management System',
      'error_during_login': 'An error occurred during login',

      // Account Status
      'account_pending_approval': 'Your account is pending approval',
      'wait_admin_approval_desc': 'Please wait for admin approval of your account',

      // Dashboard
      'assigned_tasks': 'tasks assigned to you',
      'total_requests_desc': 'Total requests',
      'pending_review_desc': 'Pending review',
      'approved_today_desc': 'Approved today',
      'avg_processing_days_desc': 'Average processing days',

      // Reports
      'no_reports_access': 'You do not have permission to access reports',
      'monthly_report': 'Monthly Report',
      'monthly_stats_desc': 'Monthly statistics of requests and approvals',
      'requests_label': 'Requests',
      'approved_label': 'Approved',
      'rejected_label': 'Rejected',

      // Requests
      'back_button': 'Back',
      'request_details': 'Request Details',
      'download_decision': 'Download Decision',

      // Gender and Personal Info
      'gender_label': 'Gender',
      'male_label': 'Male',
      'female_label': 'Female',
      'marital_status_label': 'Marital Status',
      'married_label': 'Married',
      'single_label': 'Single',
      'divorced_label': 'Divorced',
      'widowed_label': 'Widowed',

      // Common UI Text
      'or': 'or',
      'no_account': 'Don\'t have an account?',
      'demo_accounts': 'Demo Accounts:',
      'applicant': 'Applicant:',
      'staff_member': 'Staff Member:',

      // Access Control
      'access_denied': 'Access Denied',
      'no_beneficiary_access': 'You do not have permission to access beneficiary management',
      'no_registration_access': 'You do not have permission to register new beneficiaries',
    }
  }
};

// Initialize i18n immediately with resources
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'ar',
    lng: 'ar', // Set default language
    debug: process.env.NODE_ENV === 'development',
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
    },
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;


