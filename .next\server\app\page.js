/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Ci18n-provider.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Ci18n-provider.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./providers/i18n-provider.tsx */ \"(ssr)/./providers/i18n-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./providers/session-provider.tsx */ \"(ssr)/./providers/session-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Ci18n-provider.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBQ21DO0FBRzFELFNBQVNDLGNBQWMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQTJCO0lBQ3RFLHFCQUFPLDhEQUFDRixzREFBa0JBO1FBQUUsR0FBR0UsS0FBSztrQkFBR0Q7Ozs7OztBQUN6QyIsInNvdXJjZXMiOlsid2VicGFjazovL2FwcC8uL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXIudHN4PzkyODkiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSBcIm5leHQtdGhlbWVzXCJcbmltcG9ydCB7IHR5cGUgVGhlbWVQcm92aWRlclByb3BzIH0gZnJvbSBcIm5leHQtdGhlbWVzL2Rpc3QvdHlwZXNcIlxuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBUaGVtZVByb3ZpZGVyUHJvcHMpIHtcbiAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJUaGVtZVByb3ZpZGVyIiwiTmV4dFRoZW1lc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJwcm9wcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! i18next */ \"(ssr)/./node_modules/i18next/dist/esm/i18next.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! i18next-browser-languagedetector */ \"(ssr)/./node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js\");\n\n\n\nconst resources = {\n    ar: {\n        translation: {\n            // Navigation\n            \"dashboard\": \"لوحة التحكم\",\n            \"profile\": \"الملف الشخصي\",\n            \"requests\": \"الطلبات\",\n            \"tasks\": \"المهام\",\n            \"reports\": \"التقارير\",\n            \"settings\": \"الإعدادات\",\n            \"logout\": \"تسجيل الخروج\",\n            // Authentication\n            \"login\": \"تسجيل الدخول\",\n            \"register\": \"إنشاء حساب\",\n            \"email\": \"البريد الإلكتروني\",\n            \"password\": \"كلمة المرور\",\n            \"full_name\": \"الاسم الكامل\",\n            \"national_id\": \"رقم الهوية الوطنية\",\n            \"phone_number\": \"رقم الهاتف\",\n            \"login_tawtheeq\": \"تسجيل الدخول عبر توثيق\",\n            \"login_success\": \"تم تسجيل الدخول بنجاح\",\n            \"login_error\": \"خطأ في البيانات المدخلة\",\n            // Dashboard Enhancements\n            \"welcome\": \"مرحباً\",\n            \"overview_status\": \"نظرة عامة على حالة\",\n            \"system_overview\": \"النظام\",\n            \"your_requests\": \"طلباتك\",\n            \"total_beneficiaries\": \"إجمالي المستفيدين\",\n            \"total_zakat_distributed\": \"إجمالي الزكاة الموزعة\",\n            \"pending_applications\": \"الطلبات المعلقة\",\n            \"active_distributions\": \"التوزيعات النشطة\",\n            \"quick_actions\": \"الإجراءات السريعة\",\n            \"register_new_beneficiary\": \"تسجيل مستفيد جديد\",\n            \"search_beneficiaries\": \"البحث عن المستفيدين\",\n            \"generate_report\": \"إنشاء تقرير\",\n            \"manage_distributions\": \"إدارة التوزيعات\",\n            \"recent_activity\": \"النشاط الأخير\",\n            \"distribution_overview\": \"نظرة عامة على التوزيع\",\n            \"monthly_distributions\": \"التوزيعات الشهرية\",\n            \"beneficiary_categories\": \"فئات المستفيدين\",\n            \"view_all\": \"عرض الكل\",\n            \"this_month\": \"هذا الشهر\",\n            \"last_month\": \"الشهر الماضي\",\n            \"last_week\": \"الأسبوع الماضي\",\n            \"this_week\": \"هذا الأسبوع\",\n            \"increase\": \"زيادة\",\n            \"decrease\": \"انخفاض\",\n            \"from\": \"من\",\n            \"sar\": \"ريال سعودي\",\n            \"beneficiaries\": \"مستفيدين\",\n            \"applications\": \"طلبات\",\n            \"distributions\": \"توزيعات\",\n            \"completed\": \"مكتمل\",\n            \"pending\": \"معلق\",\n            \"warning\": \"تحذير\",\n            // Profile\n            \"personal_profile\": \"الملف الشخصي\",\n            \"basic_info\": \"المعلومات الأساسية\",\n            \"family_info\": \"بيانات الأسرة\",\n            \"employment_info\": \"بيانات العمل\",\n            \"financial_info\": \"المعلومات المالية\",\n            \"marital_status\": \"الحالة الاجتماعية\",\n            \"single\": \"أعزب\",\n            \"married\": \"متزوج\",\n            \"divorced\": \"مطلق\",\n            \"widowed\": \"أرمل\",\n            // Assistance Requests\n            \"assistance_request\": \"طلب المساعدة\",\n            \"new_request\": \"طلب جديد\",\n            \"request_type\": \"نوع المساعدة\",\n            \"requested_amount\": \"المبلغ المطلوب\",\n            \"description\": \"الوصف\",\n            \"attach_documents\": \"إرفاق المستندات\",\n            \"submit_request\": \"إرسال الطلب\",\n            // Status\n            \"draft\": \"مسودة\",\n            \"submitted\": \"مرسل\",\n            // Workflow Stages\n            \"reception_review\": \"مراجعة الاستقبال\",\n            \"researcher_review\": \"مراجعة الباحث\",\n            \"banking_expert_review\": \"مراجعة الخبير المصرفي\",\n            \"department_head_review\": \"مراجعة رئيس القسم\",\n            \"admin_manager_review\": \"مراجعة مدير الإدارة\",\n            \"minister_review\": \"مراجعة الوزير\",\n            // Actions\n            \"approve\": \"موافقة\",\n            \"reject\": \"رفض\",\n            \"return\": \"إرجاع\",\n            \"save\": \"حفظ\",\n            \"edit\": \"تعديل\",\n            \"delete\": \"حذف\",\n            \"view\": \"عرض\",\n            \"download\": \"تحميل\",\n            \"upload\": \"رفع\",\n            // Common\n            \"loading\": \"جاري التحميل...\",\n            \"search\": \"بحث\",\n            \"filter\": \"تصفية\",\n            \"date\": \"التاريخ\",\n            \"amount\": \"المبلغ\",\n            \"status\": \"الحالة\",\n            \"notes\": \"الملاحظات\",\n            \"documents\": \"المستندات\",\n            \"history\": \"التاريخ\",\n            // Dashboard Stats\n            \"total_requests\": \"إجمالي الطلبات\",\n            \"pending_review\": \"في انتظار المراجعة\",\n            \"approved_today\": \"موافق عليها اليوم\",\n            \"rejected_today\": \"مرفوضة اليوم\",\n            \"average_processing_days\": \"متوسط أيام المعالجة\",\n            \"total_users\": \"إجمالي المستخدمين\",\n            // User Roles\n            \"zakat_applicant\": \"مقدم طلب الزكاة\",\n            \"reception_staff\": \"موظف الاستقبال\",\n            \"researcher\": \"الباحث\",\n            \"banking_expert\": \"الخبير المصرفي\",\n            \"department_head\": \"رئيس القسم\",\n            \"admin_manager\": \"مدير الإدارة\",\n            \"minister\": \"الوزير\",\n            \"system_admin\": \"مسؤول النظام\",\n            // Beneficiary Management\n            \"beneficiary_management\": \"إدارة المستفيدين\",\n            \"beneficiary_management_desc\": \"إدارة وتتبع المستفيدين من الزكاة والمساعدات\",\n            \"beneficiary_registration\": \"تسجيل مستفيد جديد\",\n            \"beneficiary_registration_desc\": \"إضافة مستفيد جديد إلى نظام إدارة الزكاة\",\n            \"beneficiary_list\": \"قائمة المستفيدين\",\n            \"beneficiary_profile\": \"الملف الشخصي للمستفيد\",\n            \"beneficiary_details\": \"تفاصيل المستفيد\",\n            \"beneficiary_not_found\": \"المستفيد غير موجود\",\n            \"beneficiary_not_found_desc\": \"لم يتم العثور على المستفيد المطلوب\",\n            \"back_to_beneficiaries\": \"العودة إلى قائمة المستفيدين\",\n            // Beneficiary Stats\n            \"approved_beneficiaries\": \"المعتمدين\",\n            \"under_review_beneficiaries\": \"قيد المراجعة\",\n            \"total_distributions\": \"إجمالي التوزيعات\",\n            \"average_distribution\": \"متوسط التوزيع\",\n            \"pending_verification_count\": \"في انتظار التحقق\",\n            \"needs_review\": \"يحتاج إلى مراجعة\",\n            \"of_total\": \"من الإجمالي\",\n            \"per_beneficiary\": \"للمستفيد\",\n            // Search and Filters\n            \"search_and_filter\": \"البحث والتصفية\",\n            \"search_placeholder\": \"البحث بالاسم، رقم الهوية، أو رقم الهاتف...\",\n            \"filter_by_status\": \"تصفية بالحالة\",\n            \"filter_by_category\": \"تصفية بالفئة\",\n            \"all_statuses\": \"جميع الحالات\",\n            \"all_categories\": \"جميع الفئات\",\n            \"no_results_found\": \"لا توجد نتائج مطابقة لمعايير البحث\",\n            \"showing_results\": \"عرض {{count}} من أصل {{total}} مستفيد\",\n            \"export\": \"تصدير\",\n            // Beneficiary Status\n            \"suspended\": \"معلق\",\n            \"inactive\": \"غير نشط\",\n            // Zakat Categories\n            \"fuqara\": \"الفقراء\",\n            \"masakin\": \"المساكين\",\n            \"amilin\": \"العاملين عليها\",\n            \"muallafah\": \"المؤلفة قلوبهم\",\n            \"riqab\": \"في الرقاب\",\n            \"gharimin\": \"الغارمين\",\n            \"fisabilillah\": \"في سبيل الله\",\n            \"ibnus_sabil\": \"ابن السبيل\",\n            \"primary_category\": \"الفئة الأساسية\",\n            // Table Headers\n            \"name\": \"الاسم\",\n            \"total_received\": \"إجمالي المستلم\",\n            // Actions\n            \"view_profile\": \"عرض الملف الشخصي\",\n            \"manage_case\": \"إدارة الحالة\",\n            \"generate_voucher\": \"إنشاء قسيمة\",\n            \"back\": \"العودة\",\n            // Profile Tabs\n            \"overview\": \"نظرة عامة\",\n            \"personal_details\": \"البيانات الشخصية\",\n            \"eligibility_verification\": \"الأهلية والتحقق\",\n            \"case_management\": \"إدارة الحالة\",\n            \"distribution_history\": \"سجل التوزيعات\",\n            \"family\": \"الأسرة\",\n            // Profile Details\n            \"eligibility_score\": \"نقاط الأهلية\",\n            \"high_score\": \"درجة عالية\",\n            \"family_size\": \"حجم الأسرة\",\n            \"dependents\": \"معالين\",\n            \"last_distribution\": \"آخر توزيع\",\n            \"no_distribution\": \"لا يوجد\",\n            \"not_distributed_yet\": \"لم يتم التوزيع بعد\",\n            // Contact Information\n            \"contact_info\": \"معلومات الاتصال\",\n            \"account_status\": \"حالة الحساب\",\n            \"current_status\": \"الحالة الحالية\",\n            \"registration_date\": \"تاريخ التسجيل\",\n            \"next_review\": \"المراجعة القادمة\",\n            // Personal Information\n            \"personal_information\": \"البيانات الشخصية\",\n            \"basic_information\": \"المعلومات الأساسية\",\n            \"name_arabic\": \"الاسم بالعربية\",\n            \"name_english\": \"الاسم بالإنجليزية\",\n            \"date_of_birth\": \"تاريخ الميلاد\",\n            \"gender\": \"الجنس\",\n            \"male\": \"ذكر\",\n            \"female\": \"أنثى\",\n            // Family Members\n            \"family_members\": \"أفراد الأسرة\",\n            \"no_family_info\": \"لا توجد معلومات عن أفراد الأسرة\",\n            \"dependent\": \"معال\",\n            \"special_needs\": \"احتياجات خاصة\",\n            \"relationship\": \"القرابة\",\n            \"age\": \"العمر\",\n            \"years\": \"سنة\",\n            \"son\": \"ابن\",\n            \"daughter\": \"ابنة\",\n            \"mother\": \"أم\",\n            \"father\": \"أب\",\n            // Documents\n            \"no_documents\": \"لا توجد مستندات مرفوعة\",\n            \"verified\": \"محقق\",\n            \"pending_verification\": \"في انتظار التحقق\",\n            \"upload_date\": \"تاريخ الرفع\",\n            // Coming Soon\n            \"coming_soon\": \"قريباً\",\n            \"under_development\": \"قيد التطوير\",\n            \"registration_form_coming\": \"نموذج التسجيل قيد التطوير\",\n            \"registration_form_desc\": \"سيتم إضافة نموذج تسجيل المستفيدين الجدد قريباً\",\n            \"will_include_features\": \"سيتضمن النموذج الميزات التالية:\",\n            \"multi_step_form\": \"نموذج متعدد الخطوات مع مؤشر التقدم\",\n            \"dual_language_input\": \"إدخال البيانات الشخصية بالعربية والإنجليزية\",\n            \"zakat_categories_selection\": \"اختيار فئات الزكاة الثمانية\",\n            \"document_upload\": \"رفع المستندات المطلوبة\",\n            \"data_validation\": \"التحقق من صحة البيانات\",\n            \"duplicate_detection\": \"كشف التكرار التلقائي\",\n            // System Information\n            \"system_name\": \"نظام إدارة الزكاة\",\n            \"system_description\": \"نظام شامل لإدارة طلبات الزكاة والمساعدات\",\n            // Authentication Messages\n            \"create_new_account\": \"إنشاء حساب جديد\",\n            \"sign_in_to_account\": \"تسجيل الدخول إلى حسابك\",\n            \"choose_login_method\": \"اختر طريقة تسجيل الدخول المناسبة\",\n            \"verifying\": \"جاري التحقق...\",\n            \"error\": \"خطأ\",\n            \"passwords_not_match\": \"كلمات المرور غير متطابقة\",\n            \"account_created_success\": \"تم إنشاء الحساب بنجاح\",\n            \"wait_admin_approval\": \"يرجى انتظار موافقة الإدارة على حسابك\",\n            \"account_creation_error\": \"خطأ في إنشاء الحساب\",\n            \"unexpected_error\": \"حدث خطأ غير متوقع\",\n            \"error_during_creation\": \"حدث خطأ أثناء إنشاء الحساب\",\n            \"invalid_credentials\": \"البيانات المدخلة غير صحيحة\",\n            \"welcome_to_system\": \"مرحباً بك في نظام إدارة الزكاة\",\n            \"error_during_login\": \"حدث خطأ أثناء تسجيل الدخول\",\n            // Account Status\n            \"account_pending_approval\": \"حسابك في انتظار الموافقة\",\n            \"wait_admin_approval_desc\": \"يرجى انتظار موافقة الإدارة على حسابك\",\n            // Dashboard\n            \"assigned_tasks\": \"المهام المخصصة لك\",\n            \"total_requests_desc\": \"إجمالي الطلبات\",\n            \"pending_review_desc\": \"في انتظار المراجعة\",\n            \"approved_today_desc\": \"موافق عليها اليوم\",\n            \"avg_processing_days_desc\": \"متوسط أيام المعالجة\",\n            // Reports\n            \"no_reports_access\": \"ليس لديك صلاحية للوصول إلى التقارير\",\n            \"monthly_report\": \"التقرير الشهري\",\n            \"monthly_stats_desc\": \"إحصائيات الطلبات والموافقات الشهرية\",\n            \"requests_label\": \"الطلبات\",\n            \"approved_label\": \"موافق\",\n            \"rejected_label\": \"مرفوض\",\n            // Requests\n            \"back_button\": \"العودة\",\n            \"request_details\": \"تفاصيل الطلب\",\n            \"download_decision\": \"تحميل القرار\",\n            // Gender and Personal Info\n            \"gender_label\": \"الجنس\",\n            \"male_label\": \"ذكر\",\n            \"female_label\": \"أنثى\",\n            \"marital_status_label\": \"الحالة الاجتماعية\",\n            \"married_label\": \"متزوج\",\n            \"single_label\": \"أعزب\",\n            \"divorced_label\": \"مطلق\",\n            \"widowed_label\": \"أرمل\",\n            // Common UI Text\n            \"or\": \"أو\",\n            \"no_account\": \"ليس لديك حساب؟\",\n            \"demo_accounts\": \"حسابات تجريبية:\",\n            \"applicant\": \"مقدم طلب:\",\n            \"staff_member\": \"موظف:\",\n            // Access Control\n            \"access_denied\": \"غير مصرح\",\n            \"no_beneficiary_access\": \"ليس لديك صلاحية للوصول إلى إدارة المستفيدين\",\n            \"no_registration_access\": \"ليس لديك صلاحية لتسجيل مستفيدين جدد\"\n        }\n    },\n    en: {\n        translation: {\n            // Navigation\n            \"dashboard\": \"Dashboard\",\n            \"profile\": \"Profile\",\n            \"requests\": \"Requests\",\n            \"tasks\": \"Tasks\",\n            \"reports\": \"Reports\",\n            \"settings\": \"Settings\",\n            \"logout\": \"Logout\",\n            // Authentication\n            \"login\": \"Login\",\n            \"register\": \"Register\",\n            \"email\": \"Email\",\n            \"password\": \"Password\",\n            \"full_name\": \"Full Name\",\n            \"national_id\": \"National ID\",\n            \"phone_number\": \"Phone Number\",\n            \"login_tawtheeq\": \"Login with Tawtheeq\",\n            \"login_success\": \"Login successful\",\n            \"login_error\": \"Invalid credentials\",\n            // Dashboard Enhancements\n            \"welcome\": \"Welcome\",\n            \"overview_status\": \"Overview of\",\n            \"system_overview\": \"system status\",\n            \"your_requests\": \"your requests\",\n            \"total_beneficiaries\": \"Total Beneficiaries\",\n            \"total_zakat_distributed\": \"Total Zakat Distributed\",\n            \"pending_applications\": \"Pending Applications\",\n            \"active_distributions\": \"Active Distributions\",\n            \"quick_actions\": \"Quick Actions\",\n            \"register_new_beneficiary\": \"Register New Beneficiary\",\n            \"search_beneficiaries\": \"Search Beneficiaries\",\n            \"generate_report\": \"Generate Report\",\n            \"manage_distributions\": \"Manage Distributions\",\n            \"recent_activity\": \"Recent Activity\",\n            \"distribution_overview\": \"Distribution Overview\",\n            \"monthly_distributions\": \"Monthly Distributions\",\n            \"beneficiary_categories\": \"Beneficiary Categories\",\n            \"view_all\": \"View All\",\n            \"this_month\": \"This Month\",\n            \"last_month\": \"Last Month\",\n            \"last_week\": \"Last Week\",\n            \"this_week\": \"This Week\",\n            \"increase\": \"increase\",\n            \"decrease\": \"decrease\",\n            \"from\": \"from\",\n            \"sar\": \"SAR\",\n            \"beneficiaries\": \"beneficiaries\",\n            \"applications\": \"applications\",\n            \"distributions\": \"distributions\",\n            \"completed\": \"Completed\",\n            \"pending\": \"Pending\",\n            \"warning\": \"Warning\",\n            // Profile\n            \"personal_profile\": \"Personal Profile\",\n            \"basic_info\": \"Basic Information\",\n            \"family_info\": \"Family Information\",\n            \"employment_info\": \"Employment Information\",\n            \"financial_info\": \"Financial Information\",\n            \"marital_status\": \"Marital Status\",\n            \"single\": \"Single\",\n            \"married\": \"Married\",\n            \"divorced\": \"Divorced\",\n            \"widowed\": \"Widowed\",\n            // Assistance Requests\n            \"assistance_request\": \"Assistance Request\",\n            \"new_request\": \"New Request\",\n            \"request_type\": \"Request Type\",\n            \"requested_amount\": \"Requested Amount\",\n            \"description\": \"Description\",\n            \"attach_documents\": \"Attach Documents\",\n            \"submit_request\": \"Submit Request\",\n            // Status\n            \"draft\": \"Draft\",\n            \"submitted\": \"Submitted\",\n            // Workflow Stages\n            \"reception_review\": \"Reception Review\",\n            \"researcher_review\": \"Researcher Review\",\n            \"banking_expert_review\": \"Banking Expert Review\",\n            \"department_head_review\": \"Department Head Review\",\n            \"admin_manager_review\": \"Admin Manager Review\",\n            \"minister_review\": \"Minister Review\",\n            // Actions\n            \"approve\": \"Approve\",\n            \"reject\": \"Reject\",\n            \"return\": \"Return\",\n            \"save\": \"Save\",\n            \"edit\": \"Edit\",\n            \"delete\": \"Delete\",\n            \"view\": \"View\",\n            \"download\": \"Download\",\n            \"upload\": \"Upload\",\n            // Common\n            \"loading\": \"Loading...\",\n            \"search\": \"Search\",\n            \"filter\": \"Filter\",\n            \"date\": \"Date\",\n            \"amount\": \"Amount\",\n            \"status\": \"Status\",\n            \"notes\": \"Notes\",\n            \"documents\": \"Documents\",\n            \"history\": \"History\",\n            // Dashboard Stats\n            \"total_requests\": \"Total Requests\",\n            \"pending_review\": \"Pending Review\",\n            \"approved_today\": \"Approved Today\",\n            \"rejected_today\": \"Rejected Today\",\n            \"average_processing_days\": \"Avg. Processing Days\",\n            \"total_users\": \"Total Users\",\n            // User Roles\n            \"zakat_applicant\": \"Zakat Applicant\",\n            \"reception_staff\": \"Reception Staff\",\n            \"researcher\": \"Researcher\",\n            \"banking_expert\": \"Banking Expert\",\n            \"department_head\": \"Department Head\",\n            \"admin_manager\": \"Administration Manager\",\n            \"minister\": \"Minister\",\n            \"system_admin\": \"System Administrator\",\n            // Beneficiary Management\n            \"beneficiary_management\": \"Beneficiary Management\",\n            \"beneficiary_management_desc\": \"Manage and track Zakat and assistance beneficiaries\",\n            \"beneficiary_registration\": \"New Beneficiary Registration\",\n            \"beneficiary_registration_desc\": \"Add a new beneficiary to the Zakat management system\",\n            \"beneficiary_list\": \"Beneficiary List\",\n            \"beneficiary_profile\": \"Beneficiary Profile\",\n            \"beneficiary_details\": \"Beneficiary Details\",\n            \"beneficiary_not_found\": \"Beneficiary Not Found\",\n            \"beneficiary_not_found_desc\": \"The requested beneficiary could not be found\",\n            \"back_to_beneficiaries\": \"Back to Beneficiaries List\",\n            // Beneficiary Stats\n            \"approved_beneficiaries\": \"Approved\",\n            \"under_review_beneficiaries\": \"Under Review\",\n            \"total_distributions\": \"Total Distributions\",\n            \"average_distribution\": \"Average Distribution\",\n            \"pending_verification_count\": \"Pending Verification\",\n            \"needs_review\": \"Needs Review\",\n            \"of_total\": \"of Total\",\n            \"per_beneficiary\": \"per Beneficiary\",\n            // Search and Filters\n            \"search_and_filter\": \"Search and Filter\",\n            \"search_placeholder\": \"Search by name, national ID, or phone number...\",\n            \"filter_by_status\": \"Filter by Status\",\n            \"filter_by_category\": \"Filter by Category\",\n            \"all_statuses\": \"All Statuses\",\n            \"all_categories\": \"All Categories\",\n            \"no_results_found\": \"No results found matching search criteria\",\n            \"showing_results\": \"Showing {{count}} of {{total}} beneficiaries\",\n            \"export\": \"Export\",\n            // Beneficiary Status\n            \"pending_verification\": \"Pending Verification\",\n            \"under_review\": \"Under Review\",\n            \"approved\": \"Approved\",\n            \"rejected\": \"Rejected\",\n            \"suspended\": \"Suspended\",\n            \"inactive\": \"Inactive\",\n            // Zakat Categories\n            \"fuqara\": \"The Poor\",\n            \"masakin\": \"The Needy\",\n            \"amilin\": \"Zakat Administrators\",\n            \"muallafah\": \"Those whose hearts are reconciled\",\n            \"riqab\": \"To free slaves/captives\",\n            \"gharimin\": \"Those in debt\",\n            \"fisabilillah\": \"In the cause of Allah\",\n            \"ibnus_sabil\": \"The wayfarer/traveler\",\n            \"primary_category\": \"Primary Category\",\n            // Table Headers\n            \"name\": \"Name\",\n            \"total_received\": \"Total Received\",\n            // Actions\n            \"view_profile\": \"View Profile\",\n            \"manage_case\": \"Manage Case\",\n            \"generate_voucher\": \"Generate Voucher\",\n            \"back\": \"Back\",\n            // Profile Tabs\n            \"overview\": \"Overview\",\n            \"personal_details\": \"Personal Details\",\n            \"eligibility_verification\": \"Eligibility & Verification\",\n            \"case_management\": \"Case Management\",\n            \"distribution_history\": \"Distribution History\",\n            \"family\": \"Family\",\n            // Profile Details\n            \"eligibility_score\": \"Eligibility Score\",\n            \"high_score\": \"High Score\",\n            \"family_size\": \"Family Size\",\n            \"dependents\": \"Dependents\",\n            \"last_distribution\": \"Last Distribution\",\n            \"no_distribution\": \"None\",\n            \"not_distributed_yet\": \"Not distributed yet\",\n            // Contact Information\n            \"contact_info\": \"Contact Information\",\n            \"account_status\": \"Account Status\",\n            \"current_status\": \"Current Status\",\n            \"registration_date\": \"Registration Date\",\n            \"next_review\": \"Next Review\",\n            // Personal Information\n            \"personal_information\": \"Personal Information\",\n            \"basic_information\": \"Basic Information\",\n            \"name_arabic\": \"Name in Arabic\",\n            \"name_english\": \"Name in English\",\n            \"date_of_birth\": \"Date of Birth\",\n            \"gender\": \"Gender\",\n            \"male\": \"Male\",\n            \"female\": \"Female\",\n            // Family Members\n            \"family_members\": \"Family Members\",\n            \"no_family_info\": \"No family information available\",\n            \"dependent\": \"Dependent\",\n            \"special_needs\": \"Special Needs\",\n            \"relationship\": \"Relationship\",\n            \"age\": \"Age\",\n            \"years\": \"years\",\n            \"son\": \"Son\",\n            \"daughter\": \"Daughter\",\n            \"mother\": \"Mother\",\n            \"father\": \"Father\",\n            // Documents\n            \"no_documents\": \"No documents uploaded\",\n            \"verified\": \"Verified\",\n            \"upload_date\": \"Upload Date\",\n            // Coming Soon\n            \"coming_soon\": \"Coming Soon\",\n            \"under_development\": \"Under Development\",\n            \"registration_form_coming\": \"Registration Form Under Development\",\n            \"registration_form_desc\": \"New beneficiary registration form will be added soon\",\n            \"will_include_features\": \"The form will include the following features:\",\n            \"multi_step_form\": \"Multi-step form with progress indicator\",\n            \"dual_language_input\": \"Personal data input in Arabic and English\",\n            \"zakat_categories_selection\": \"Selection of eight Zakat categories\",\n            \"document_upload\": \"Required document upload\",\n            \"data_validation\": \"Data validation\",\n            \"duplicate_detection\": \"Automatic duplicate detection\",\n            // System Information\n            \"system_name\": \"Zakat Management System\",\n            \"system_description\": \"Comprehensive system for managing Zakat and assistance requests\",\n            // Authentication Messages\n            \"create_new_account\": \"Create new account\",\n            \"sign_in_to_account\": \"Sign in to your account\",\n            \"choose_login_method\": \"Choose the appropriate login method\",\n            \"verifying\": \"Verifying...\",\n            \"error\": \"Error\",\n            \"passwords_not_match\": \"Passwords do not match\",\n            \"account_created_success\": \"Account created successfully\",\n            \"wait_admin_approval\": \"Please wait for admin approval of your account\",\n            \"account_creation_error\": \"Error creating account\",\n            \"unexpected_error\": \"An unexpected error occurred\",\n            \"error_during_creation\": \"An error occurred during account creation\",\n            \"invalid_credentials\": \"Invalid credentials entered\",\n            \"welcome_to_system\": \"Welcome to the Zakat Management System\",\n            \"error_during_login\": \"An error occurred during login\",\n            // Account Status\n            \"account_pending_approval\": \"Your account is pending approval\",\n            \"wait_admin_approval_desc\": \"Please wait for admin approval of your account\",\n            // Dashboard\n            \"assigned_tasks\": \"tasks assigned to you\",\n            \"total_requests_desc\": \"Total requests\",\n            \"pending_review_desc\": \"Pending review\",\n            \"approved_today_desc\": \"Approved today\",\n            \"avg_processing_days_desc\": \"Average processing days\",\n            // Reports\n            \"no_reports_access\": \"You do not have permission to access reports\",\n            \"monthly_report\": \"Monthly Report\",\n            \"monthly_stats_desc\": \"Monthly statistics of requests and approvals\",\n            \"requests_label\": \"Requests\",\n            \"approved_label\": \"Approved\",\n            \"rejected_label\": \"Rejected\",\n            // Requests\n            \"back_button\": \"Back\",\n            \"request_details\": \"Request Details\",\n            \"download_decision\": \"Download Decision\",\n            // Gender and Personal Info\n            \"gender_label\": \"Gender\",\n            \"male_label\": \"Male\",\n            \"female_label\": \"Female\",\n            \"marital_status_label\": \"Marital Status\",\n            \"married_label\": \"Married\",\n            \"single_label\": \"Single\",\n            \"divorced_label\": \"Divorced\",\n            \"widowed_label\": \"Widowed\",\n            // Common UI Text\n            \"or\": \"or\",\n            \"no_account\": \"Don't have an account?\",\n            \"demo_accounts\": \"Demo Accounts:\",\n            \"applicant\": \"Applicant:\",\n            \"staff_member\": \"Staff Member:\",\n            // Access Control\n            \"access_denied\": \"Access Denied\",\n            \"no_beneficiary_access\": \"You do not have permission to access beneficiary management\",\n            \"no_registration_access\": \"You do not have permission to register new beneficiaries\"\n        }\n    }\n};\n// Initialize i18n immediately with resources\ni18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"].use(i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_2__[\"default\"]).use(react_i18next__WEBPACK_IMPORTED_MODULE_1__.initReactI18next).init({\n    resources,\n    fallbackLng: \"ar\",\n    lng: \"ar\",\n    debug: \"development\" === \"development\",\n    detection: {\n        order: [\n            \"localStorage\",\n            \"navigator\",\n            \"htmlTag\"\n        ],\n        caches: [\n            \"localStorage\"\n        ]\n    },\n    interpolation: {\n        escapeValue: false\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (i18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n.ts\n");

/***/ }),

/***/ "(ssr)/./providers/i18n-provider.tsx":
/*!*************************************!*\
  !*** ./providers/i18n-provider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nProvider: () => (/* binding */ I18nProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/i18n */ \"(ssr)/./lib/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ I18nProvider auto */ \n\n\nfunction I18nProvider({ children }) {\n    // Always render the I18nextProvider to avoid hydration mismatch\n    // The i18n instance is already initialized with resources in lib/i18n.ts\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_i18next__WEBPACK_IMPORTED_MODULE_1__.I18nextProvider, {\n        i18n: _lib_i18n__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\providers\\\\i18n-provider.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9wcm92aWRlcnMvaTE4bi1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBSStDO0FBQ2xCO0FBTXRCLFNBQVNFLGFBQWEsRUFBRUMsUUFBUSxFQUFxQjtJQUMxRCxnRUFBZ0U7SUFDaEUseUVBQXlFO0lBQ3pFLHFCQUNFLDhEQUFDSCwwREFBZUE7UUFBQ0MsTUFBTUEsaURBQUlBO2tCQUN4QkU7Ozs7OztBQUdQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBwLy4vcHJvdmlkZXJzL2kxOG4tcHJvdmlkZXIudHN4PzVkYjIiXSwic291cmNlc0NvbnRlbnQiOlsiXG4ndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBJMThuZXh0UHJvdmlkZXIgfSBmcm9tICdyZWFjdC1pMThuZXh0J1xuaW1wb3J0IGkxOG4gZnJvbSAnQC9saWIvaTE4bidcblxuaW50ZXJmYWNlIEkxOG5Qcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZVxufVxuXG5leHBvcnQgZnVuY3Rpb24gSTE4blByb3ZpZGVyKHsgY2hpbGRyZW4gfTogSTE4blByb3ZpZGVyUHJvcHMpIHtcbiAgLy8gQWx3YXlzIHJlbmRlciB0aGUgSTE4bmV4dFByb3ZpZGVyIHRvIGF2b2lkIGh5ZHJhdGlvbiBtaXNtYXRjaFxuICAvLyBUaGUgaTE4biBpbnN0YW5jZSBpcyBhbHJlYWR5IGluaXRpYWxpemVkIHdpdGggcmVzb3VyY2VzIGluIGxpYi9pMThuLnRzXG4gIHJldHVybiAoXG4gICAgPEkxOG5leHRQcm92aWRlciBpMThuPXtpMThufT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0kxOG5leHRQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkkxOG5leHRQcm92aWRlciIsImkxOG4iLCJJMThuUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./providers/i18n-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./providers/session-provider.tsx":
/*!****************************************!*\
  !*** ./providers/session-provider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ SessionProvider auto */ \n\nfunction SessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\providers\\\\session-provider.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9wcm92aWRlcnMvc2Vzc2lvbi1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRzRFO0FBT3JFLFNBQVNBLGdCQUFnQixFQUFFRSxRQUFRLEVBQXdCO0lBQ2hFLHFCQUFPLDhEQUFDRCw0REFBdUJBO2tCQUFFQzs7Ozs7O0FBQ25DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBwLy4vcHJvdmlkZXJzL3Nlc3Npb24tcHJvdmlkZXIudHN4PzIzNWEiXSwic291cmNlc0NvbnRlbnQiOlsiXG4ndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIGFzIE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0J1xuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5cbmludGVyZmFjZSBTZXNzaW9uUHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFNlc3Npb25Qcm92aWRlcih7IGNoaWxkcmVuIH06IFNlc3Npb25Qcm92aWRlclByb3BzKSB7XG4gIHJldHVybiA8TmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+e2NoaWxkcmVufTwvTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./providers/session-provider.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"42bb61ef2a3f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcHAvLi9hcHAvZ2xvYmFscy5jc3M/YjcwMSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjQyYmI2MWVmMmEzZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _providers_session_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/session-provider */ \"(rsc)/./providers/session-provider.tsx\");\n/* harmony import */ var _providers_i18n_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/i18n-provider */ \"(rsc)/./providers/i18n-provider.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"نظام إدارة الزكاة - Zakat Management System\",\n    description: \"نظام شامل لإدارة طلبات الزكاة والمساعدات\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"light\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_session_provider__WEBPACK_IMPORTED_MODULE_2__.SessionProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_i18n_provider__WEBPACK_IMPORTED_MODULE_3__.I18nProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n\nfunction HomePage() {\n    (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.redirect)(\"/auth/login\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7QUFDMEM7QUFFM0IsU0FBU0M7SUFDdEJELHlEQUFRQSxDQUFDO0FBQ1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcHAvLi9hcHAvcGFnZS50c3g/NzYwMyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmltcG9ydCB7IHJlZGlyZWN0IH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lUGFnZSgpIHtcbiAgcmVkaXJlY3QoJy9hdXRoL2xvZ2luJylcbn1cbiJdLCJuYW1lcyI6WyJyZWRpcmVjdCIsIkhvbWVQYWdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\VCode projects\zakat-deepagent\zakat_management_system\app\components\theme-provider.tsx#ThemeProvider`);


/***/ }),

/***/ "(rsc)/./providers/i18n-provider.tsx":
/*!*************************************!*\
  !*** ./providers/i18n-provider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   I18nProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\VCode projects\zakat-deepagent\zakat_management_system\app\providers\i18n-provider.tsx#I18nProvider`);


/***/ }),

/***/ "(rsc)/./providers/session-provider.tsx":
/*!****************************************!*\
  !*** ./providers/session-provider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SessionProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\VCode projects\zakat-deepagent\zakat_management_system\app\providers\session-provider.tsx#SessionProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/i18next","vendor-chunks/react-i18next","vendor-chunks/i18next-browser-languagedetector","vendor-chunks/next-themes","vendor-chunks/@swc","vendor-chunks/html-parse-stringify","vendor-chunks/void-elements"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();