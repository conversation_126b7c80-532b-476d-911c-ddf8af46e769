@import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Islamic-inspired color palette */
    --background: 0 0% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Deep Islamic blue for primary */
    --primary: 214 88% 27%;
    --primary-foreground: 0 0% 98%;
    --primary-light: 214 95% 50%;

    /* Warm secondary tones */
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    /* Improved muted colors */
    --muted: 220 14% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    /* Islamic green for success/prosperity */
    --accent: 160 84% 39%;
    --accent-foreground: 0 0% 98%;
    --success: 160 84% 39%;
    --success-foreground: 0 0% 98%;

    /* Warm warning color */
    --warning: 25 95% 53%;
    --warning-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    /* Subtle borders and inputs */
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 214 88% 27%;

    --radius: 0.75rem;

    /* Custom design tokens */
    --shadow-soft: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-medium: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-large: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

    /* Gradient backgrounds */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-light)) 100%);
    --gradient-success: linear-gradient(135deg, hsl(var(--success)) 0%, hsl(160 84% 45%) 100%);
    --gradient-subtle: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(220 14% 96%) 100%);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 214 95% 50%;
    --primary-foreground: 222.2 84% 4.9%;
    --primary-light: 214 95% 60%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 160 84% 45%;
    --accent-foreground: 210 40% 98%;
    --success: 160 84% 45%;
    --success-foreground: 210 40% 98%;

    --warning: 25 95% 53%;
    --warning-foreground: 222.2 84% 4.9%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 214 95% 50%;

    /* Dark theme gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-light)) 100%);
    --gradient-success: linear-gradient(135deg, hsl(var(--success)) 0%, hsl(160 84% 50%) 100%);
    --gradient-subtle: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(217.2 32.6% 20%) 100%);
  }
}
 
@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Arabic text styling */
  [dir="rtl"] {
    font-family: 'Amiri', 'Noto Naskh Arabic', serif;
  }

  /* Enhanced typography */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-3xl lg:text-4xl;
    line-height: 1.2;
  }

  h2 {
    @apply text-2xl lg:text-3xl;
    line-height: 1.3;
  }

  h3 {
    @apply text-xl lg:text-2xl;
    line-height: 1.4;
  }
}

@layer components {
  /* Enhanced card styling */
  .card-enhanced {
    @apply bg-card border border-border rounded-lg shadow-sm;
    box-shadow: var(--shadow-soft);
    transition: all 0.2s ease-in-out;
  }

  .card-enhanced:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-1px);
  }

  /* Gradient backgrounds */
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-success {
    background: var(--gradient-success);
  }

  .bg-gradient-subtle {
    background: var(--gradient-subtle);
  }

  /* Islamic geometric pattern (subtle) */
  .pattern-islamic {
    background-image:
      radial-gradient(circle at 25% 25%, hsl(var(--primary) / 0.05) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, hsl(var(--success) / 0.05) 0%, transparent 50%);
    background-size: 60px 60px;
    background-position: 0 0, 30px 30px;
  }

  /* Enhanced button styles */
  .btn-enhanced {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-all;
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
    @apply disabled:pointer-events-none disabled:opacity-50;
    box-shadow: var(--shadow-soft);
  }

  .btn-enhanced:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
  }

  /* Enhanced input styles */
  .input-enhanced {
    @apply flex h-10 w-full rounded-lg border border-border bg-background px-3 py-2 text-sm;
    @apply ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium;
    @apply placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2;
    @apply focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
    transition: all 0.2s ease-in-out;
    box-shadow: var(--shadow-soft);
  }

  .input-enhanced:focus {
    border-color: hsl(var(--primary));
    box-shadow: var(--shadow-medium);
  }

  /* Islamic decorative elements */
  .islamic-border {
    position: relative;
  }

  .islamic-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, hsl(var(--primary)), transparent);
  }

  /* Animated gradient text */
  .gradient-text {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--success)));
    background-size: 200% 200%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-shift 3s ease-in-out infinite;
  }

  @keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  /* Floating animation */
  .float-animation {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  /* Pulse animation for notifications */
  .pulse-success {
    animation: pulse-success 2s infinite;
  }

  @keyframes pulse-success {
    0%, 100% { box-shadow: 0 0 0 0 hsl(var(--success) / 0.7); }
    70% { box-shadow: 0 0 0 10px hsl(var(--success) / 0); }
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--primary) / 0.3);
    border-radius: 4px;
    transition: background 0.2s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary) / 0.5);
  }

  /* Selection styling */
  ::selection {
    background: hsl(var(--primary) / 0.2);
    color: hsl(var(--primary));
  }

  /* Focus ring improvements */
  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2;
  }

  /* Glass morphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Shimmer loading effect */
  .shimmer {
    background: linear-gradient(90deg,
      hsl(var(--muted)) 0%,
      hsl(var(--muted-foreground) / 0.1) 50%,
      hsl(var(--muted)) 100%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }
}