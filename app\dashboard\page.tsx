
'use client'

import { useSession } from 'next-auth/react'
import { useTranslation } from 'react-i18next'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { EnhancedStats, StatsGrid } from '@/components/dashboard/enhanced-stats'
import { QuickActions } from '@/components/dashboard/quick-actions'
import { RecentActivity } from '@/components/dashboard/recent-activity'
import { WelcomeBanner } from '@/components/dashboard/welcome-banner'
import {
  Users,
  Clock,
  TrendingUp,
  DollarSign
} from 'lucide-react'

// Mock dashboard data - replace with real data from your mock-data.ts
const mockDashboardData = {
  totalBeneficiaries: 1247,
  totalZakatDistributed: 2450000,
  pendingApplications: 23,
  activeDistributions: 8,
  changes: {
    beneficiaries: { value: 12, type: 'increase' as const, period: 'last_month' },
    zakat: { value: 8, type: 'increase' as const, period: 'last_month' },
    pending: { value: 5, type: 'decrease' as const, period: 'last_week' },
    distributions: { value: 2, type: 'increase' as const, period: 'this_week' }
  }
}

export default function DashboardPage() {
  const { data: session } = useSession() || {}
  const { t } = useTranslation()

  if (!session?.user) {
    return null
  }

  const userRole = session.user.role
  const data = mockDashboardData

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Welcome Banner */}
        <WelcomeBanner
          userName={session.user.name || ''}
          userRole={userRole}
          className="mb-2"
        />

        {/* Enhanced Statistics */}
        <StatsGrid>
          <EnhancedStats
            title={t('total_beneficiaries')}
            value={data.totalBeneficiaries.toLocaleString()}
            change={data.changes.beneficiaries}
            icon={Users}
            color="success"
            trend={[45, 52, 48, 61, 58, 67, 72]}
          />
          <EnhancedStats
            title={t('total_zakat_distributed')}
            value={`${(data.totalZakatDistributed / 1000000).toFixed(1)}M ${t('sar')}`}
            change={data.changes.zakat}
            icon={DollarSign}
            color="primary"
            trend={[30, 35, 42, 38, 45, 50, 48]}
          />
          <EnhancedStats
            title={t('pending_applications')}
            value={data.pendingApplications}
            change={data.changes.pending}
            icon={Clock}
            color="warning"
            trend={[25, 28, 22, 30, 26, 23, 20]}
          />
          <EnhancedStats
            title={t('active_distributions')}
            value={data.activeDistributions}
            change={data.changes.distributions}
            icon={TrendingUp}
            color="info"
            trend={[15, 18, 22, 19, 25, 28, 30]}
          />
        </StatsGrid>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Quick Actions - Takes 2 columns on large screens */}
          <div className="lg:col-span-2">
            <QuickActions />
          </div>

          {/* Recent Activity - Takes 1 column on large screens */}
          <div className="lg:col-span-1">
            <RecentActivity />
          </div>
        </div>

        {/* Additional Charts Section - Placeholder for future implementation */}
        <div className="grid gap-6 md:grid-cols-2">
          <div className="h-64 rounded-xl border-2 border-dashed border-primary/20 bg-gradient-subtle flex items-center justify-center card-enhanced">
            <div className="text-center space-y-3">
              <div className="h-16 w-16 mx-auto rounded-full bg-primary/10 flex items-center justify-center">
                <TrendingUp className="h-8 w-8 text-primary" />
              </div>
              <div>
                <p className="text-lg font-semibold text-foreground">{t('monthly_distributions')}</p>
                <p className="text-sm text-muted-foreground">Chart coming soon</p>
              </div>
            </div>
          </div>
          <div className="h-64 rounded-xl border-2 border-dashed border-success/20 bg-gradient-subtle flex items-center justify-center card-enhanced">
            <div className="text-center space-y-3">
              <div className="h-16 w-16 mx-auto rounded-full bg-success/10 flex items-center justify-center">
                <Users className="h-8 w-8 text-success" />
              </div>
              <div>
                <p className="text-lg font-semibold text-foreground">{t('beneficiary_categories')}</p>
                <p className="text-sm text-muted-foreground">Chart coming soon</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}


