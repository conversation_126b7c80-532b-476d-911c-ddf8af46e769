
'use client'

import { useSession } from 'next-auth/react'
import { useTranslation } from 'react-i18next'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { SummaryWidget } from '@/components/dashboard/summary-widget'
import { QuickActions } from '@/components/dashboard/quick-actions'
import { RecentActivity } from '@/components/dashboard/recent-activity'
import { 
  Users, 
  CreditCard, 
  Clock, 
  TrendingUp,
  DollarSign
} from 'lucide-react'

// Mock dashboard data - replace with real data from your mock-data.ts
const mockDashboardData = {
  totalBeneficiaries: 1247,
  totalZakatDistributed: 2450000,
  pendingApplications: 23,
  activeDistributions: 8,
  changes: {
    beneficiaries: { value: 12, type: 'increase' as const, period: 'last_month' },
    zakat: { value: 8, type: 'increase' as const, period: 'last_month' },
    pending: { value: 5, type: 'decrease' as const, period: 'last_week' },
    distributions: { value: 2, type: 'increase' as const, period: 'this_week' }
  }
}

export default function DashboardPage() {
  const { data: session } = useSession() || {}
  const { t } = useTranslation()

  if (!session?.user) {
    return null
  }

  const userRole = session.user.role
  const data = mockDashboardData

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">
            {t('welcome')}, {session.user.name}
          </h1>
          <p className="text-muted-foreground">
            {t('overview_status')} {userRole === 'zakat_applicant' ? t('your_requests') : t('system_overview')}
          </p>
        </div>

        {/* Summary Widgets */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <SummaryWidget
            title={t('total_beneficiaries')}
            value={data.totalBeneficiaries.toLocaleString()}
            change={data.changes.beneficiaries}
            icon={Users}
            color="success"
          />
          <SummaryWidget
            title={t('total_zakat_distributed')}
            value={`${(data.totalZakatDistributed / 1000000).toFixed(1)}M ${t('sar')}`}
            change={data.changes.zakat}
            icon={DollarSign}
            color="success"
          />
          <SummaryWidget
            title={t('pending_applications')}
            value={data.pendingApplications}
            change={data.changes.pending}
            icon={Clock}
            color="warning"
          />
          <SummaryWidget
            title={t('active_distributions')}
            value={data.activeDistributions}
            change={data.changes.distributions}
            icon={TrendingUp}
            color="default"
          />
        </div>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Quick Actions - Takes 2 columns on large screens */}
          <div className="lg:col-span-2">
            <QuickActions />
          </div>

          {/* Recent Activity - Takes 1 column on large screens */}
          <div className="lg:col-span-1">
            <RecentActivity />
          </div>
        </div>

        {/* Additional Charts Section - Placeholder for future implementation */}
        <div className="grid gap-6 md:grid-cols-2">
          <div className="h-64 rounded-lg border-2 border-dashed border-muted-foreground/25 flex items-center justify-center">
            <div className="text-center space-y-2">
              <TrendingUp className="h-8 w-8 mx-auto text-muted-foreground" />
              <p className="text-sm text-muted-foreground">{t('monthly_distributions')}</p>
              <p className="text-xs text-muted-foreground">Chart coming soon</p>
            </div>
          </div>
          <div className="h-64 rounded-lg border-2 border-dashed border-muted-foreground/25 flex items-center justify-center">
            <div className="text-center space-y-2">
              <Users className="h-8 w-8 mx-auto text-muted-foreground" />
              <p className="text-sm text-muted-foreground">{t('beneficiary_categories')}</p>
              <p className="text-xs text-muted-foreground">Chart coming soon</p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}


