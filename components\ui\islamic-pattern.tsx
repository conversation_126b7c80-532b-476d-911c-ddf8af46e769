'use client'

import React from 'react'
import { cn } from '@/lib/utils'

interface IslamicPatternProps {
  className?: string
  variant?: 'subtle' | 'light' | 'medium'
  children?: React.ReactNode
}

export function IslamicPattern({ 
  className, 
  variant = 'subtle', 
  children 
}: IslamicPatternProps) {
  const patternVariants = {
    subtle: 'opacity-30',
    light: 'opacity-50', 
    medium: 'opacity-70'
  }

  return (
    <div className={cn('relative', className)}>
      {/* Islamic geometric pattern SVG */}
      <div 
        className={cn(
          'absolute inset-0 pointer-events-none',
          patternVariants[variant]
        )}
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '60px 60px'
        }}
      />
      {children}
    </div>
  )
}

// Alternative star pattern
export function IslamicStarPattern({ 
  className, 
  variant = 'subtle', 
  children 
}: IslamicPatternProps) {
  const patternVariants = {
    subtle: 'opacity-20',
    light: 'opacity-40', 
    medium: 'opacity-60'
  }

  return (
    <div className={cn('relative', className)}>
      <div 
        className={cn(
          'absolute inset-0 pointer-events-none',
          patternVariants[variant]
        )}
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%231e40af' fill-opacity='0.08'%3E%3Cpath d='M20 20l-8-8 8-8 8 8-8 8zm0 0l8 8-8 8-8-8 8-8z'/%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '40px 40px'
        }}
      />
      {children}
    </div>
  )
}
