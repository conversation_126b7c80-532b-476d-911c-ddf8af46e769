
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { SessionProvider } from '@/providers/session-provider'
import { I18nProvider } from '@/providers/i18n-provider'
import { ThemeProvider } from '@/components/theme-provider'

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter'
})

export const metadata: Metadata = {
  title: 'نظام إدارة الزكاة - Zakat Management System',
  description: 'نظام شامل لإدارة طلبات الزكاة والمساعدات',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`} suppressHydrationWarning>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <SessionProvider>
            <I18nProvider>
              {children}
            </I18nProvider>
          </SessionProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
