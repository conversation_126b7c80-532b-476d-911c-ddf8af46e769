'use client'

import React from 'react'
import { useTranslation } from 'react-i18next'
import { Card } from '@/components/ui/card'
import { IslamicPattern } from '@/components/ui/islamic-pattern'
import { cn } from '@/lib/utils'

interface WelcomeBannerProps {
  userName: string
  userRole: string
  className?: string
}

export function WelcomeBanner({ userName, userRole, className }: WelcomeBannerProps) {
  const { t } = useTranslation()

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'system_admin':
        return 'from-primary to-primary-light'
      case 'reception_staff':
        return 'from-success to-green-400'
      case 'case_manager':
        return 'from-warning to-orange-400'
      default:
        return 'from-primary to-primary-light'
    }
  }

  return (
    <Card className={cn('relative overflow-hidden border-0 bg-gradient-subtle', className)}>
      <IslamicPattern variant="subtle">
        <div className="relative p-8">
          {/* Background gradient overlay */}
          <div className={cn(
            'absolute inset-0 bg-gradient-to-r opacity-10',
            getRoleColor(userRole)
          )} />
          
          {/* Content */}
          <div className="relative z-10 flex items-center space-x-6 rtl:space-x-reverse">
            {/* Avatar */}
            <div className={cn(
              'h-20 w-20 rounded-full bg-gradient-to-r flex items-center justify-center shadow-large',
              getRoleColor(userRole)
            )}>
              <span className="text-white font-bold text-2xl">
                {userName?.charAt(0).toUpperCase()}
              </span>
            </div>
            
            {/* Welcome text */}
            <div className="flex-1">
              <h1 className="text-4xl font-bold mb-2">
                <span className="gradient-text">
                  {t('welcome')}, {userName}
                </span>
              </h1>
              <p className="text-lg text-muted-foreground mb-1">
                {t('role')}: <span className="font-semibold text-foreground">{t(userRole)}</span>
              </p>
              <p className="text-muted-foreground">
                {t('dashboard_subtitle')}
              </p>
            </div>
            
            {/* Decorative element */}
            <div className="hidden md:block">
              <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center float-animation">
                <div className="h-8 w-8 rounded-full bg-gradient-primary" />
              </div>
            </div>
          </div>
          
          {/* Islamic decorative border */}
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-primary to-transparent opacity-30" />
        </div>
      </IslamicPattern>
    </Card>
  )
}

// Compact version for smaller screens
export function CompactWelcomeBanner({ userName, userRole, className }: WelcomeBannerProps) {
  const { t } = useTranslation()

  return (
    <Card className={cn('relative overflow-hidden border-0 bg-gradient-subtle', className)}>
      <div className="p-4">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <div className="h-12 w-12 rounded-full bg-gradient-primary flex items-center justify-center shadow-medium">
            <span className="text-primary-foreground font-bold text-lg">
              {userName?.charAt(0).toUpperCase()}
            </span>
          </div>
          <div className="flex-1">
            <h2 className="text-xl font-bold gradient-text">
              {t('welcome')}, {userName}
            </h2>
            <p className="text-sm text-muted-foreground">
              {t(userRole)}
            </p>
          </div>
        </div>
      </div>
    </Card>
  )
}
