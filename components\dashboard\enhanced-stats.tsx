'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { LucideIcon } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { cn } from '@/lib/utils'

interface EnhancedStatsProps {
  title: string
  value: string | number
  change?: {
    value: number
    type: 'increase' | 'decrease'
    period: string
  }
  icon: LucideIcon
  color?: 'primary' | 'success' | 'warning' | 'info'
  trend?: number[]
  className?: string
}

export function EnhancedStats({ 
  title, 
  value, 
  change, 
  icon: Icon, 
  color = 'primary',
  trend,
  className
}: EnhancedStatsProps) {
  const { t } = useTranslation()

  const colorConfig = {
    primary: {
      bg: 'bg-gradient-primary',
      text: 'text-primary-foreground',
      accent: 'bg-primary/10',
      border: 'border-primary/20'
    },
    success: {
      bg: 'bg-gradient-success',
      text: 'text-success-foreground',
      accent: 'bg-success/10',
      border: 'border-success/20'
    },
    warning: {
      bg: 'bg-warning',
      text: 'text-warning-foreground',
      accent: 'bg-warning/10',
      border: 'border-warning/20'
    },
    info: {
      bg: 'bg-blue-500',
      text: 'text-white',
      accent: 'bg-blue-500/10',
      border: 'border-blue-500/20'
    }
  }

  const config = colorConfig[color]

  return (
    <Card className={cn(
      'relative overflow-hidden border-0 bg-card/80 backdrop-blur-sm hover:shadow-large transition-all duration-300 group',
      className
    )}>
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className={cn('h-full w-full', config.accent)} />
      </div>
      
      {/* Animated background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      
      <CardContent className="relative p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-muted-foreground mb-2">
              {title}
            </p>
            <div className="flex items-baseline space-x-2 rtl:space-x-reverse">
              <h3 className="text-3xl font-bold tracking-tight text-foreground">
                {value}
              </h3>
              {change && (
                <Badge 
                  variant="secondary"
                  className={cn(
                    'text-xs font-medium px-2 py-1',
                    change.type === 'increase' 
                      ? 'bg-success/20 text-success border-success/30' 
                      : 'bg-destructive/20 text-destructive border-destructive/30'
                  )}
                >
                  {change.type === 'increase' ? '↗' : '↘'} {Math.abs(change.value)}%
                </Badge>
              )}
            </div>
            {change && (
              <p className="text-xs text-muted-foreground mt-2">
                {t(change.type)} {t('from')} {t(change.period)}
              </p>
            )}
          </div>
          
          {/* Icon with animated background */}
          <div className={cn(
            'h-14 w-14 rounded-xl flex items-center justify-center shadow-soft group-hover:shadow-medium transition-all duration-300 group-hover:scale-110',
            config.bg
          )}>
            <Icon className={cn('h-7 w-7', config.text)} />
          </div>
        </div>
        
        {/* Mini trend chart */}
        {trend && (
          <div className="mt-4 flex items-end space-x-1 h-8">
            {trend.map((value, index) => (
              <div
                key={index}
                className={cn(
                  'flex-1 rounded-sm transition-all duration-300 group-hover:opacity-80',
                  config.accent
                )}
                style={{ 
                  height: `${(value / Math.max(...trend)) * 100}%`,
                  minHeight: '4px'
                }}
              />
            ))}
          </div>
        )}
      </CardContent>
      
      {/* Decorative border */}
      <div className={cn(
        'absolute bottom-0 left-0 right-0 h-1 transition-all duration-300',
        config.bg,
        'opacity-0 group-hover:opacity-100'
      )} />
    </Card>
  )
}

// Grid container for stats
export function StatsGrid({ children, className }: { children: React.ReactNode, className?: string }) {
  return (
    <div className={cn(
      'grid gap-6 md:grid-cols-2 lg:grid-cols-4',
      className
    )}>
      {children}
    </div>
  )
}
