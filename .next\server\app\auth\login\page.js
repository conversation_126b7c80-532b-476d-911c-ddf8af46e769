/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/login/page";
exports.ids = ["app/auth/login/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/auth/login/page.tsx */ \"(rsc)/./app/auth/login/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/auth/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/login/page\",\n        pathname: \"/auth/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/auth/login/page.tsx */ \"(ssr)/./app/auth/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2l2YWhyJTVDJTVDT25lRHJpdmUlNUMlNUNEb2N1bWVudHMlNUMlNUNWQ29kZSUyMHByb2plY3RzJTVDJTVDemFrYXQtZGVlcGFnZW50JTVDJTVDemFrYXRfbWFuYWdlbWVudF9zeXN0ZW0lNUMlNUNhcHAlNUMlNUNhcHAlNUMlNUNhdXRoJTVDJTVDbG9naW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQXFLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBwLz9jODBjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcaXZhaHJcXFxcT25lRHJpdmVcXFxcRG9jdW1lbnRzXFxcXFZDb2RlIHByb2plY3RzXFxcXHpha2F0LWRlZXBhZ2VudFxcXFx6YWthdF9tYW5hZ2VtZW50X3N5c3RlbVxcXFxhcHBcXFxcYXBwXFxcXGF1dGhcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Ci18n-provider.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Ci18n-provider.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./providers/i18n-provider.tsx */ \"(ssr)/./providers/i18n-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./providers/session-provider.tsx */ \"(ssr)/./providers/session-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Ci18n-provider.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/auth/login/page.tsx":
/*!*********************************!*\
  !*** ./app/auth/login/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_use_safe_translation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-safe-translation */ \"(ssr)/./hooks/use-safe-translation.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_language_switcher__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/language-switcher */ \"(ssr)/./components/ui/language-switcher.tsx\");\n/* harmony import */ var _barrel_optimize_names_Lock_LogIn_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Lock,LogIn,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_Lock_LogIn_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Lock,LogIn,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Lock_LogIn_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Lock,LogIn,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Lock_LogIn_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Lock,LogIn,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\nfunction LoginPage() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTawtheeqLogin, setIsTawtheeqLogin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { t, i18n } = (0,_hooks_use_safe_translation__WEBPACK_IMPORTED_MODULE_4__.useSafeTranslation)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        try {\n            const result = await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signIn)(\"credentials\", {\n                email,\n                password,\n                redirect: false\n            });\n            if (result?.error) {\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                    title: t(\"login_error\"),\n                    description: t(\"invalid_credentials\"),\n                    variant: \"destructive\"\n                });\n            } else {\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                    title: t(\"login_success\"),\n                    description: t(\"welcome_to_system\")\n                });\n                router.push(\"/dashboard\");\n            }\n        } catch (error) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: t(\"error\"),\n                description: t(\"error_during_login\"),\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleTawtheeqLogin = ()=>{\n        setIsTawtheeqLogin(true);\n        // Mock Tawtheeq integration - in real app this would redirect to Tawtheeq\n        setTimeout(()=>{\n            (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signIn)(\"credentials\", {\n                email: \"<EMAIL>\",\n                password: \"johndoe123\",\n                redirect: false\n            }).then((result)=>{\n                if (!result?.error) {\n                    router.push(\"/dashboard\");\n                }\n                setIsTawtheeqLogin(false);\n            });\n        }, 2000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-md space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-16 w-16 rounded-full bg-primary flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-primary-foreground font-bold text-2xl\",\n                                    children: \"ز\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: t(\"system_name\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: t(\"sign_in_to_account\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_language_switcher__WEBPACK_IMPORTED_MODULE_9__.LanguageSwitcher, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lock_LogIn_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        t(\"login\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardDescription, {\n                                    children: t(\"choose_login_method\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: handleTawtheeqLogin,\n                                    disabled: isTawtheeqLogin,\n                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                    children: isTawtheeqLogin ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 19\n                                            }, this),\n                                            t(\"verifying\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lock_LogIn_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 19\n                                            }, this),\n                                            t(\"login_tawtheeq\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-full border-t\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex justify-center text-xs uppercase\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-background px-2 text-muted-foreground\",\n                                                children: t(\"or\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                    htmlFor: \"email\",\n                                                    children: t(\"email\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lock_LogIn_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"absolute right-3 top-3 h-4 w-4 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            id: \"email\",\n                                                            type: \"email\",\n                                                            placeholder: \"<EMAIL>\",\n                                                            value: email,\n                                                            onChange: (e)=>setEmail(e.target.value),\n                                                            required: true,\n                                                            className: \"pr-10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                    htmlFor: \"password\",\n                                                    children: t(\"password\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lock_LogIn_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"absolute right-3 top-3 h-4 w-4 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            id: \"password\",\n                                                            type: \"password\",\n                                                            value: password,\n                                                            onChange: (e)=>setPassword(e.target.value),\n                                                            required: true,\n                                                            className: \"pr-10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            type: \"submit\",\n                                            className: \"w-full\",\n                                            disabled: isLoading,\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    t(\"loading\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, this) : t(\"login\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: [\n                                                t(\"no_account\"),\n                                                \" \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            href: \"/auth/register\",\n                                            className: \"text-primary hover:underline\",\n                                            children: t(\"register\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                    className: \"bg-blue-50 border-blue-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-blue-900\",\n                                    children: t(\"demo_accounts\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-blue-700 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: t(\"system_admin\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 20\n                                                }, this),\n                                                \" <EMAIL> / johndoe123\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: t(\"applicant\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 20\n                                                }, this),\n                                                \" <EMAIL> / johndoe123\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: t(\"staff_member\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 20\n                                                }, this),\n                                                \" <EMAIL> / johndoe123\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/auth/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBQ21DO0FBRzFELFNBQVNDLGNBQWMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQTJCO0lBQ3RFLHFCQUFPLDhEQUFDRixzREFBa0JBO1FBQUUsR0FBR0UsS0FBSztrQkFBR0Q7Ozs7OztBQUN6QyIsInNvdXJjZXMiOlsid2VicGFjazovL2FwcC8uL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXIudHN4PzkyODkiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSBcIm5leHQtdGhlbWVzXCJcbmltcG9ydCB7IHR5cGUgVGhlbWVQcm92aWRlclByb3BzIH0gZnJvbSBcIm5leHQtdGhlbWVzL2Rpc3QvdHlwZXNcIlxuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBUaGVtZVByb3ZpZGVyUHJvcHMpIHtcbiAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJUaGVtZVByb3ZpZGVyIiwiTmV4dFRoZW1lc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJwcm9wcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-soft hover:shadow-medium hover:-translate-y-0.5\", {\n    variants: {\n        variant: {\n            default: \"bg-gradient-primary text-primary-foreground hover:opacity-90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-border bg-background hover:bg-accent hover:text-accent-foreground hover:border-primary/30\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground shadow-none hover:shadow-soft\",\n            link: \"text-primary underline-offset-4 hover:underline shadow-none hover:shadow-none hover:translate-y-0\",\n            success: \"bg-gradient-success text-success-foreground hover:opacity-90\",\n            warning: \"bg-warning text-warning-foreground hover:bg-warning/90\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-lg px-3\",\n            lg: \"h-12 rounded-xl px-8 text-base\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 48,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-xl border border-border/50 bg-card text-card-foreground shadow-soft transition-all duration-200 hover:shadow-medium\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2NhcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBRWhDLE1BQU1FLHFCQUFPRiw2Q0FBZ0IsQ0FHM0IsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUNYLCtIQUNBRztRQUVELEdBQUdDLEtBQUs7Ozs7OztBQUdiSCxLQUFLTSxXQUFXLEdBQUc7QUFFbkIsTUFBTUMsMkJBQWFULDZDQUFnQixDQUdqQyxDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUMxQiw4REFBQ0M7UUFDQ0QsS0FBS0E7UUFDTEYsV0FBV0gsOENBQUVBLENBQUMsaUNBQWlDRztRQUM5QyxHQUFHQyxLQUFLOzs7Ozs7QUFHYkksV0FBV0QsV0FBVyxHQUFHO0FBRXpCLE1BQU1FLDBCQUFZViw2Q0FBZ0IsQ0FHaEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNLO1FBQ0NMLEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUNYLHNEQUNBRztRQUVELEdBQUdDLEtBQUs7Ozs7OztBQUdiSyxVQUFVRixXQUFXLEdBQUc7QUFFeEIsTUFBTUksZ0NBQWtCWiw2Q0FBZ0IsQ0FHdEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNPO1FBQ0NQLEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUFDLGlDQUFpQ0c7UUFDOUMsR0FBR0MsS0FBSzs7Ozs7O0FBR2JPLGdCQUFnQkosV0FBVyxHQUFHO0FBRTlCLE1BQU1NLDRCQUFjZCw2Q0FBZ0IsQ0FHbEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQUlELEtBQUtBO1FBQUtGLFdBQVdILDhDQUFFQSxDQUFDLFlBQVlHO1FBQWEsR0FBR0MsS0FBSzs7Ozs7O0FBRWhFUyxZQUFZTixXQUFXLEdBQUc7QUFFMUIsTUFBTU8sMkJBQWFmLDZDQUFnQixDQUdqQyxDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUMxQiw4REFBQ0M7UUFDQ0QsS0FBS0E7UUFDTEYsV0FBV0gsOENBQUVBLENBQUMsOEJBQThCRztRQUMzQyxHQUFHQyxLQUFLOzs7Ozs7QUFHYlUsV0FBV1AsV0FBVyxHQUFHO0FBRXVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBwLy4vY29tcG9uZW50cy91aS9jYXJkLnRzeD9hZDkxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgQ2FyZCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGRpdlxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcInJvdW5kZWQteGwgYm9yZGVyIGJvcmRlci1ib3JkZXIvNTAgYmctY2FyZCB0ZXh0LWNhcmQtZm9yZWdyb3VuZCBzaGFkb3ctc29mdCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgaG92ZXI6c2hhZG93LW1lZGl1bVwiLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQ2FyZC5kaXNwbGF5TmFtZSA9IFwiQ2FyZFwiXG5cbmNvbnN0IENhcmRIZWFkZXIgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MRGl2RWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxkaXZcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFwiZmxleCBmbGV4LWNvbCBzcGFjZS15LTEuNSBwLTZcIiwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQ2FyZEhlYWRlci5kaXNwbGF5TmFtZSA9IFwiQ2FyZEhlYWRlclwiXG5cbmNvbnN0IENhcmRUaXRsZSA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxQYXJhZ3JhcGhFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MSGVhZGluZ0VsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxoM1xuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcInRleHQtMnhsIGZvbnQtc2VtaWJvbGQgbGVhZGluZy1ub25lIHRyYWNraW5nLXRpZ2h0XCIsXG4gICAgICBjbGFzc05hbWVcbiAgICApfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5DYXJkVGl0bGUuZGlzcGxheU5hbWUgPSBcIkNhcmRUaXRsZVwiXG5cbmNvbnN0IENhcmREZXNjcmlwdGlvbiA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxQYXJhZ3JhcGhFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MUGFyYWdyYXBoRWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPHBcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIiwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQ2FyZERlc2NyaXB0aW9uLmRpc3BsYXlOYW1lID0gXCJDYXJkRGVzY3JpcHRpb25cIlxuXG5jb25zdCBDYXJkQ29udGVudCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGRpdiByZWY9e3JlZn0gY2xhc3NOYW1lPXtjbihcInAtNiBwdC0wXCIsIGNsYXNzTmFtZSl9IHsuLi5wcm9wc30gLz5cbikpXG5DYXJkQ29udGVudC5kaXNwbGF5TmFtZSA9IFwiQ2FyZENvbnRlbnRcIlxuXG5jb25zdCBDYXJkRm9vdGVyID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcImZsZXggaXRlbXMtY2VudGVyIHAtNiBwdC0wXCIsIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmRGb290ZXIuZGlzcGxheU5hbWUgPSBcIkNhcmRGb290ZXJcIlxuXG5leHBvcnQgeyBDYXJkLCBDYXJkSGVhZGVyLCBDYXJkRm9vdGVyLCBDYXJkVGl0bGUsIENhcmREZXNjcmlwdGlvbiwgQ2FyZENvbnRlbnQgfSJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiQ2FyZCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsInJlZiIsImRpdiIsImRpc3BsYXlOYW1lIiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsImgzIiwiQ2FyZERlc2NyaXB0aW9uIiwicCIsIkNhcmRDb250ZW50IiwiQ2FyZEZvb3RlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/dropdown-menu.tsx":
/*!*****************************************!*\
  !*** ./components/ui/dropdown-menu.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuShortcut: () => (/* binding */ DropdownMenuShortcut),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(ssr)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DropdownMenu,DropdownMenuTrigger,DropdownMenuContent,DropdownMenuItem,DropdownMenuCheckboxItem,DropdownMenuRadioItem,DropdownMenuLabel,DropdownMenuSeparator,DropdownMenuShortcut,DropdownMenuGroup,DropdownMenuPortal,DropdownMenuSub,DropdownMenuSubContent,DropdownMenuSubTrigger,DropdownMenuRadioGroup auto */ \n\n\n\n\nconst DropdownMenu = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DropdownMenuTrigger = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DropdownMenuGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst DropdownMenuPortal = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DropdownMenuSub = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Sub;\nconst DropdownMenuRadioGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup;\nconst DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\", inset && \"pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"ml-auto h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubTrigger.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger.displayName;\nconst DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent.displayName;\nconst DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n            lineNumber: 64,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 83,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, checked, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 108,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 99,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuCheckboxItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem.displayName;\nconst DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-2 w-2 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 131,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 123,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuRadioItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem.displayName;\nconst DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 147,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuLabel.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 163,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSeparator.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\nconst DropdownMenuShortcut = ({ className, ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"ml-auto text-xs tracking-widest opacity-60\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, undefined);\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/dropdown-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFLaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGdXQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBwLy4vY29tcG9uZW50cy91aS9pbnB1dC50c3g/ZGE3OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmV4cG9ydCBpbnRlcmZhY2UgSW5wdXRQcm9wc1xuICBleHRlbmRzIFJlYWN0LklucHV0SFRNTEF0dHJpYnV0ZXM8SFRNTElucHV0RWxlbWVudD4ge31cblxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIElucHV0UHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8aW5wdXRcbiAgICAgICAgdHlwZT17dHlwZX1cbiAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICBcImZsZXggaC0xMCB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtc20gcmluZy1vZmZzZXQtYmFja2dyb3VuZCBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MFwiLFxuICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICApfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbklucHV0LmRpc3BsYXlOYW1lID0gXCJJbnB1dFwiXG5cbmV4cG9ydCB7IElucHV0IH0iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsInJlZiIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/label.tsx":
/*!*********************************!*\
  !*** ./components/ui/label.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFOEI7QUFDeUI7QUFDVTtBQUVqQztBQUVoQyxNQUFNSSxnQkFBZ0JGLDZEQUFHQSxDQUN2QjtBQUdGLE1BQU1HLHNCQUFRTCw2Q0FBZ0IsQ0FJNUIsQ0FBQyxFQUFFTyxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNSLHVEQUFtQjtRQUNsQlEsS0FBS0E7UUFDTEYsV0FBV0osOENBQUVBLENBQUNDLGlCQUFpQkc7UUFDOUIsR0FBR0MsS0FBSzs7Ozs7O0FBR2JILE1BQU1NLFdBQVcsR0FBR1YsdURBQW1CLENBQUNVLFdBQVc7QUFFbkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcHAvLi9jb21wb25lbnRzL3VpL2xhYmVsLnRzeD84OGVkIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgKiBhcyBMYWJlbFByaW1pdGl2ZSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWxhYmVsXCJcbmltcG9ydCB7IGN2YSwgdHlwZSBWYXJpYW50UHJvcHMgfSBmcm9tIFwiY2xhc3MtdmFyaWFuY2UtYXV0aG9yaXR5XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBsYWJlbFZhcmlhbnRzID0gY3ZhKFxuICBcInRleHQtc20gZm9udC1tZWRpdW0gbGVhZGluZy1ub25lIHBlZXItZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHBlZXItZGlzYWJsZWQ6b3BhY2l0eS03MFwiXG4pXG5cbmNvbnN0IExhYmVsID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4gJlxuICAgIFZhcmlhbnRQcm9wczx0eXBlb2YgbGFiZWxWYXJpYW50cz5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPExhYmVsUHJpbWl0aXZlLlJvb3RcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKGxhYmVsVmFyaWFudHMoKSwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuTGFiZWwuZGlzcGxheU5hbWUgPSBMYWJlbFByaW1pdGl2ZS5Sb290LmRpc3BsYXlOYW1lXG5cbmV4cG9ydCB7IExhYmVsIH0iXSwibmFtZXMiOlsiUmVhY3QiLCJMYWJlbFByaW1pdGl2ZSIsImN2YSIsImNuIiwibGFiZWxWYXJpYW50cyIsIkxhYmVsIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInByb3BzIiwicmVmIiwiUm9vdCIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/language-switcher.tsx":
/*!*********************************************!*\
  !*** ./components/ui/language-switcher.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageSwitcher: () => (/* binding */ LanguageSwitcher)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Globe!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./dropdown-menu */ \"(ssr)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ LanguageSwitcher auto */ \n\n\n\n\n\nfunction LanguageSwitcher() {\n    const { i18n, t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)();\n    const changeLanguage = (lng)=>{\n        i18n.changeLanguage(lng);\n        // Update HTML direction\n        document.documentElement.dir = lng === \"ar\" ? \"rtl\" : \"ltr\";\n        document.documentElement.lang = lng;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        // Set initial direction based on current language\n        document.documentElement.dir = i18n.language === \"ar\" ? \"rtl\" : \"ltr\";\n        document.documentElement.lang = i18n.language;\n    }, [\n        i18n.language\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: \"ghost\",\n                    size: \"sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-4 w-4 me-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        i18n.language === \"ar\" ? \"العربية\" : \"English\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\language-switcher.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\language-switcher.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                align: \"end\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>changeLanguage(\"ar\"),\n                        children: \"العربية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>changeLanguage(\"en\"),\n                        children: \"English\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\language-switcher.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\language-switcher.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/language-switcher.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/use-safe-translation.ts":
/*!***************************************!*\
  !*** ./hooks/use-safe-translation.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSafeTranslation: () => (/* binding */ useSafeTranslation)\n/* harmony export */ });\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useSafeTranslation auto */ \n\nfunction useSafeTranslation() {\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_0__.useTranslation)();\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    // Safe translation function that provides fallbacks to prevent hydration mismatch\n    const st = (key, fallback)=>{\n        if (!isClient) {\n            // During SSR or before hydration, return the fallback or Arabic translation\n            const translations = {\n                \"login\": \"تسجيل الدخول\",\n                \"register\": \"إنشاء حساب\",\n                \"email\": \"البريد الإلكتروني\",\n                \"password\": \"كلمة المرور\",\n                \"login_tawtheeq\": \"تسجيل الدخول عبر توثيق\",\n                \"login_success\": \"تم تسجيل الدخول بنجاح\",\n                \"login_error\": \"خطأ في البيانات المدخلة\",\n                \"dashboard\": \"لوحة التحكم\",\n                \"profile\": \"الملف الشخصي\",\n                \"requests\": \"الطلبات\",\n                \"tasks\": \"المهام\",\n                \"reports\": \"التقارير\",\n                \"settings\": \"الإعدادات\",\n                \"logout\": \"تسجيل الخروج\",\n                \"welcome\": \"مرحباً\",\n                \"overview_status\": \"نظرة عامة على حالة\",\n                \"system_overview\": \"النظام\",\n                \"total_beneficiaries\": \"إجمالي المستفيدين\",\n                \"total_zakat_distributed\": \"إجمالي الزكاة الموزعة\",\n                \"pending_applications\": \"الطلبات المعلقة\",\n                \"active_distributions\": \"التوزيعات النشطة\",\n                \"quick_actions\": \"الإجراءات السريعة\",\n                \"recent_activity\": \"النشاط الأخير\",\n                \"monthly_distributions\": \"التوزيعات الشهرية\",\n                \"beneficiary_categories\": \"فئات المستفيدين\"\n            };\n            return fallback || translations[key] || key;\n        }\n        return t(key);\n    };\n    return {\n        t: st,\n        i18n,\n        isClient\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/use-safe-translation.ts\n");

/***/ }),

/***/ "(ssr)/./hooks/use-toast.ts":
/*!****************************!*\
  !*** ./hooks/use-toast.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ // Inspired by react-hot-toast library\n\nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        listeners.push(setState);\n        return ()=>{\n            const index = listeners.indexOf(setState);\n            if (index > -1) {\n                listeners.splice(index, 1);\n            }\n        };\n    }, [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! i18next */ \"(ssr)/./node_modules/i18next/dist/esm/i18next.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! i18next-browser-languagedetector */ \"(ssr)/./node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js\");\n\n\n\nconst resources = {\n    ar: {\n        translation: {\n            // Navigation\n            \"dashboard\": \"لوحة التحكم\",\n            \"profile\": \"الملف الشخصي\",\n            \"requests\": \"الطلبات\",\n            \"tasks\": \"المهام\",\n            \"reports\": \"التقارير\",\n            \"settings\": \"الإعدادات\",\n            \"logout\": \"تسجيل الخروج\",\n            // Authentication\n            \"login\": \"تسجيل الدخول\",\n            \"register\": \"إنشاء حساب\",\n            \"email\": \"البريد الإلكتروني\",\n            \"password\": \"كلمة المرور\",\n            \"full_name\": \"الاسم الكامل\",\n            \"national_id\": \"رقم الهوية الوطنية\",\n            \"phone_number\": \"رقم الهاتف\",\n            \"login_tawtheeq\": \"تسجيل الدخول عبر توثيق\",\n            \"login_success\": \"تم تسجيل الدخول بنجاح\",\n            \"login_error\": \"خطأ في البيانات المدخلة\",\n            // Dashboard Enhancements\n            \"welcome\": \"مرحباً\",\n            \"overview_status\": \"نظرة عامة على حالة\",\n            \"system_overview\": \"النظام\",\n            \"your_requests\": \"طلباتك\",\n            \"total_beneficiaries\": \"إجمالي المستفيدين\",\n            \"total_zakat_distributed\": \"إجمالي الزكاة الموزعة\",\n            \"pending_applications\": \"الطلبات المعلقة\",\n            \"active_distributions\": \"التوزيعات النشطة\",\n            \"quick_actions\": \"الإجراءات السريعة\",\n            \"register_new_beneficiary\": \"تسجيل مستفيد جديد\",\n            \"search_beneficiaries\": \"البحث عن المستفيدين\",\n            \"generate_report\": \"إنشاء تقرير\",\n            \"manage_distributions\": \"إدارة التوزيعات\",\n            \"recent_activity\": \"النشاط الأخير\",\n            \"distribution_overview\": \"نظرة عامة على التوزيع\",\n            \"monthly_distributions\": \"التوزيعات الشهرية\",\n            \"beneficiary_categories\": \"فئات المستفيدين\",\n            \"view_all\": \"عرض الكل\",\n            \"this_month\": \"هذا الشهر\",\n            \"last_month\": \"الشهر الماضي\",\n            \"last_week\": \"الأسبوع الماضي\",\n            \"this_week\": \"هذا الأسبوع\",\n            \"increase\": \"زيادة\",\n            \"decrease\": \"انخفاض\",\n            \"from\": \"من\",\n            \"sar\": \"ريال سعودي\",\n            \"beneficiaries\": \"مستفيدين\",\n            \"applications\": \"طلبات\",\n            \"distributions\": \"توزيعات\",\n            \"completed\": \"مكتمل\",\n            \"pending\": \"معلق\",\n            \"warning\": \"تحذير\",\n            // Profile\n            \"personal_profile\": \"الملف الشخصي\",\n            \"basic_info\": \"المعلومات الأساسية\",\n            \"family_info\": \"بيانات الأسرة\",\n            \"employment_info\": \"بيانات العمل\",\n            \"financial_info\": \"المعلومات المالية\",\n            \"marital_status\": \"الحالة الاجتماعية\",\n            \"single\": \"أعزب\",\n            \"married\": \"متزوج\",\n            \"divorced\": \"مطلق\",\n            \"widowed\": \"أرمل\",\n            // Assistance Requests\n            \"assistance_request\": \"طلب المساعدة\",\n            \"new_request\": \"طلب جديد\",\n            \"request_type\": \"نوع المساعدة\",\n            \"requested_amount\": \"المبلغ المطلوب\",\n            \"description\": \"الوصف\",\n            \"attach_documents\": \"إرفاق المستندات\",\n            \"submit_request\": \"إرسال الطلب\",\n            // Status\n            \"draft\": \"مسودة\",\n            \"submitted\": \"مرسل\",\n            // Workflow Stages\n            \"reception_review\": \"مراجعة الاستقبال\",\n            \"researcher_review\": \"مراجعة الباحث\",\n            \"banking_expert_review\": \"مراجعة الخبير المصرفي\",\n            \"department_head_review\": \"مراجعة رئيس القسم\",\n            \"admin_manager_review\": \"مراجعة مدير الإدارة\",\n            \"minister_review\": \"مراجعة الوزير\",\n            // Actions\n            \"approve\": \"موافقة\",\n            \"reject\": \"رفض\",\n            \"return\": \"إرجاع\",\n            \"save\": \"حفظ\",\n            \"edit\": \"تعديل\",\n            \"delete\": \"حذف\",\n            \"view\": \"عرض\",\n            \"download\": \"تحميل\",\n            \"upload\": \"رفع\",\n            // Common\n            \"loading\": \"جاري التحميل...\",\n            \"search\": \"بحث\",\n            \"filter\": \"تصفية\",\n            \"date\": \"التاريخ\",\n            \"amount\": \"المبلغ\",\n            \"status\": \"الحالة\",\n            \"notes\": \"الملاحظات\",\n            \"documents\": \"المستندات\",\n            \"history\": \"التاريخ\",\n            // Dashboard Stats\n            \"total_requests\": \"إجمالي الطلبات\",\n            \"pending_review\": \"في انتظار المراجعة\",\n            \"approved_today\": \"موافق عليها اليوم\",\n            \"rejected_today\": \"مرفوضة اليوم\",\n            \"average_processing_days\": \"متوسط أيام المعالجة\",\n            \"total_users\": \"إجمالي المستخدمين\",\n            // User Roles\n            \"zakat_applicant\": \"مقدم طلب الزكاة\",\n            \"reception_staff\": \"موظف الاستقبال\",\n            \"researcher\": \"الباحث\",\n            \"banking_expert\": \"الخبير المصرفي\",\n            \"department_head\": \"رئيس القسم\",\n            \"admin_manager\": \"مدير الإدارة\",\n            \"minister\": \"الوزير\",\n            \"system_admin\": \"مسؤول النظام\",\n            // Beneficiary Management\n            \"beneficiary_management\": \"إدارة المستفيدين\",\n            \"beneficiary_management_desc\": \"إدارة وتتبع المستفيدين من الزكاة والمساعدات\",\n            \"beneficiary_registration\": \"تسجيل مستفيد جديد\",\n            \"beneficiary_registration_desc\": \"إضافة مستفيد جديد إلى نظام إدارة الزكاة\",\n            \"beneficiary_list\": \"قائمة المستفيدين\",\n            \"beneficiary_profile\": \"الملف الشخصي للمستفيد\",\n            \"beneficiary_details\": \"تفاصيل المستفيد\",\n            \"beneficiary_not_found\": \"المستفيد غير موجود\",\n            \"beneficiary_not_found_desc\": \"لم يتم العثور على المستفيد المطلوب\",\n            \"back_to_beneficiaries\": \"العودة إلى قائمة المستفيدين\",\n            // Beneficiary Stats\n            \"approved_beneficiaries\": \"المعتمدين\",\n            \"under_review_beneficiaries\": \"قيد المراجعة\",\n            \"total_distributions\": \"إجمالي التوزيعات\",\n            \"average_distribution\": \"متوسط التوزيع\",\n            \"pending_verification_count\": \"في انتظار التحقق\",\n            \"needs_review\": \"يحتاج إلى مراجعة\",\n            \"of_total\": \"من الإجمالي\",\n            \"per_beneficiary\": \"للمستفيد\",\n            // Search and Filters\n            \"search_and_filter\": \"البحث والتصفية\",\n            \"search_placeholder\": \"البحث بالاسم، رقم الهوية، أو رقم الهاتف...\",\n            \"filter_by_status\": \"تصفية بالحالة\",\n            \"filter_by_category\": \"تصفية بالفئة\",\n            \"all_statuses\": \"جميع الحالات\",\n            \"all_categories\": \"جميع الفئات\",\n            \"no_results_found\": \"لا توجد نتائج مطابقة لمعايير البحث\",\n            \"showing_results\": \"عرض {{count}} من أصل {{total}} مستفيد\",\n            \"export\": \"تصدير\",\n            // Beneficiary Status\n            \"suspended\": \"معلق\",\n            \"inactive\": \"غير نشط\",\n            // Zakat Categories\n            \"fuqara\": \"الفقراء\",\n            \"masakin\": \"المساكين\",\n            \"amilin\": \"العاملين عليها\",\n            \"muallafah\": \"المؤلفة قلوبهم\",\n            \"riqab\": \"في الرقاب\",\n            \"gharimin\": \"الغارمين\",\n            \"fisabilillah\": \"في سبيل الله\",\n            \"ibnus_sabil\": \"ابن السبيل\",\n            \"primary_category\": \"الفئة الأساسية\",\n            // Table Headers\n            \"name\": \"الاسم\",\n            \"total_received\": \"إجمالي المستلم\",\n            // Actions\n            \"view_profile\": \"عرض الملف الشخصي\",\n            \"manage_case\": \"إدارة الحالة\",\n            \"generate_voucher\": \"إنشاء قسيمة\",\n            \"back\": \"العودة\",\n            // Profile Tabs\n            \"overview\": \"نظرة عامة\",\n            \"personal_details\": \"البيانات الشخصية\",\n            \"eligibility_verification\": \"الأهلية والتحقق\",\n            \"case_management\": \"إدارة الحالة\",\n            \"distribution_history\": \"سجل التوزيعات\",\n            \"family\": \"الأسرة\",\n            // Profile Details\n            \"eligibility_score\": \"نقاط الأهلية\",\n            \"high_score\": \"درجة عالية\",\n            \"family_size\": \"حجم الأسرة\",\n            \"dependents\": \"معالين\",\n            \"last_distribution\": \"آخر توزيع\",\n            \"no_distribution\": \"لا يوجد\",\n            \"not_distributed_yet\": \"لم يتم التوزيع بعد\",\n            // Contact Information\n            \"contact_info\": \"معلومات الاتصال\",\n            \"account_status\": \"حالة الحساب\",\n            \"current_status\": \"الحالة الحالية\",\n            \"registration_date\": \"تاريخ التسجيل\",\n            \"next_review\": \"المراجعة القادمة\",\n            // Personal Information\n            \"personal_information\": \"البيانات الشخصية\",\n            \"basic_information\": \"المعلومات الأساسية\",\n            \"name_arabic\": \"الاسم بالعربية\",\n            \"name_english\": \"الاسم بالإنجليزية\",\n            \"date_of_birth\": \"تاريخ الميلاد\",\n            \"gender\": \"الجنس\",\n            \"male\": \"ذكر\",\n            \"female\": \"أنثى\",\n            // Family Members\n            \"family_members\": \"أفراد الأسرة\",\n            \"no_family_info\": \"لا توجد معلومات عن أفراد الأسرة\",\n            \"dependent\": \"معال\",\n            \"special_needs\": \"احتياجات خاصة\",\n            \"relationship\": \"القرابة\",\n            \"age\": \"العمر\",\n            \"years\": \"سنة\",\n            \"son\": \"ابن\",\n            \"daughter\": \"ابنة\",\n            \"mother\": \"أم\",\n            \"father\": \"أب\",\n            // Documents\n            \"no_documents\": \"لا توجد مستندات مرفوعة\",\n            \"verified\": \"محقق\",\n            \"pending_verification\": \"في انتظار التحقق\",\n            \"upload_date\": \"تاريخ الرفع\",\n            // Coming Soon\n            \"coming_soon\": \"قريباً\",\n            \"under_development\": \"قيد التطوير\",\n            \"registration_form_coming\": \"نموذج التسجيل قيد التطوير\",\n            \"registration_form_desc\": \"سيتم إضافة نموذج تسجيل المستفيدين الجدد قريباً\",\n            \"will_include_features\": \"سيتضمن النموذج الميزات التالية:\",\n            \"multi_step_form\": \"نموذج متعدد الخطوات مع مؤشر التقدم\",\n            \"dual_language_input\": \"إدخال البيانات الشخصية بالعربية والإنجليزية\",\n            \"zakat_categories_selection\": \"اختيار فئات الزكاة الثمانية\",\n            \"document_upload\": \"رفع المستندات المطلوبة\",\n            \"data_validation\": \"التحقق من صحة البيانات\",\n            \"duplicate_detection\": \"كشف التكرار التلقائي\",\n            // System Information\n            \"system_name\": \"نظام إدارة الزكاة\",\n            \"system_description\": \"نظام شامل لإدارة طلبات الزكاة والمساعدات\",\n            // Authentication Messages\n            \"create_new_account\": \"إنشاء حساب جديد\",\n            \"sign_in_to_account\": \"تسجيل الدخول إلى حسابك\",\n            \"choose_login_method\": \"اختر طريقة تسجيل الدخول المناسبة\",\n            \"verifying\": \"جاري التحقق...\",\n            \"error\": \"خطأ\",\n            \"passwords_not_match\": \"كلمات المرور غير متطابقة\",\n            \"account_created_success\": \"تم إنشاء الحساب بنجاح\",\n            \"wait_admin_approval\": \"يرجى انتظار موافقة الإدارة على حسابك\",\n            \"account_creation_error\": \"خطأ في إنشاء الحساب\",\n            \"unexpected_error\": \"حدث خطأ غير متوقع\",\n            \"error_during_creation\": \"حدث خطأ أثناء إنشاء الحساب\",\n            \"invalid_credentials\": \"البيانات المدخلة غير صحيحة\",\n            \"welcome_to_system\": \"مرحباً بك في نظام إدارة الزكاة\",\n            \"error_during_login\": \"حدث خطأ أثناء تسجيل الدخول\",\n            // Account Status\n            \"account_pending_approval\": \"حسابك في انتظار الموافقة\",\n            \"wait_admin_approval_desc\": \"يرجى انتظار موافقة الإدارة على حسابك\",\n            // Dashboard\n            \"assigned_tasks\": \"المهام المخصصة لك\",\n            \"total_requests_desc\": \"إجمالي الطلبات\",\n            \"pending_review_desc\": \"في انتظار المراجعة\",\n            \"approved_today_desc\": \"موافق عليها اليوم\",\n            \"avg_processing_days_desc\": \"متوسط أيام المعالجة\",\n            // Reports\n            \"no_reports_access\": \"ليس لديك صلاحية للوصول إلى التقارير\",\n            \"monthly_report\": \"التقرير الشهري\",\n            \"monthly_stats_desc\": \"إحصائيات الطلبات والموافقات الشهرية\",\n            \"requests_label\": \"الطلبات\",\n            \"approved_label\": \"موافق\",\n            \"rejected_label\": \"مرفوض\",\n            // Requests\n            \"back_button\": \"العودة\",\n            \"request_details\": \"تفاصيل الطلب\",\n            \"download_decision\": \"تحميل القرار\",\n            // Gender and Personal Info\n            \"gender_label\": \"الجنس\",\n            \"male_label\": \"ذكر\",\n            \"female_label\": \"أنثى\",\n            \"marital_status_label\": \"الحالة الاجتماعية\",\n            \"married_label\": \"متزوج\",\n            \"single_label\": \"أعزب\",\n            \"divorced_label\": \"مطلق\",\n            \"widowed_label\": \"أرمل\",\n            // Common UI Text\n            \"or\": \"أو\",\n            \"no_account\": \"ليس لديك حساب؟\",\n            \"demo_accounts\": \"حسابات تجريبية:\",\n            \"applicant\": \"مقدم طلب:\",\n            \"staff_member\": \"موظف:\",\n            // Access Control\n            \"access_denied\": \"غير مصرح\",\n            \"no_beneficiary_access\": \"ليس لديك صلاحية للوصول إلى إدارة المستفيدين\",\n            \"no_registration_access\": \"ليس لديك صلاحية لتسجيل مستفيدين جدد\"\n        }\n    },\n    en: {\n        translation: {\n            // Navigation\n            \"dashboard\": \"Dashboard\",\n            \"profile\": \"Profile\",\n            \"requests\": \"Requests\",\n            \"tasks\": \"Tasks\",\n            \"reports\": \"Reports\",\n            \"settings\": \"Settings\",\n            \"logout\": \"Logout\",\n            // Authentication\n            \"login\": \"Login\",\n            \"register\": \"Register\",\n            \"email\": \"Email\",\n            \"password\": \"Password\",\n            \"full_name\": \"Full Name\",\n            \"national_id\": \"National ID\",\n            \"phone_number\": \"Phone Number\",\n            \"login_tawtheeq\": \"Login with Tawtheeq\",\n            \"login_success\": \"Login successful\",\n            \"login_error\": \"Invalid credentials\",\n            // Dashboard Enhancements\n            \"welcome\": \"Welcome\",\n            \"overview_status\": \"Overview of\",\n            \"system_overview\": \"system status\",\n            \"your_requests\": \"your requests\",\n            \"total_beneficiaries\": \"Total Beneficiaries\",\n            \"total_zakat_distributed\": \"Total Zakat Distributed\",\n            \"pending_applications\": \"Pending Applications\",\n            \"active_distributions\": \"Active Distributions\",\n            \"quick_actions\": \"Quick Actions\",\n            \"register_new_beneficiary\": \"Register New Beneficiary\",\n            \"search_beneficiaries\": \"Search Beneficiaries\",\n            \"generate_report\": \"Generate Report\",\n            \"manage_distributions\": \"Manage Distributions\",\n            \"recent_activity\": \"Recent Activity\",\n            \"distribution_overview\": \"Distribution Overview\",\n            \"monthly_distributions\": \"Monthly Distributions\",\n            \"beneficiary_categories\": \"Beneficiary Categories\",\n            \"view_all\": \"View All\",\n            \"this_month\": \"This Month\",\n            \"last_month\": \"Last Month\",\n            \"last_week\": \"Last Week\",\n            \"this_week\": \"This Week\",\n            \"increase\": \"increase\",\n            \"decrease\": \"decrease\",\n            \"from\": \"from\",\n            \"sar\": \"SAR\",\n            \"beneficiaries\": \"beneficiaries\",\n            \"applications\": \"applications\",\n            \"distributions\": \"distributions\",\n            \"completed\": \"Completed\",\n            \"pending\": \"Pending\",\n            \"warning\": \"Warning\",\n            // Profile\n            \"personal_profile\": \"Personal Profile\",\n            \"basic_info\": \"Basic Information\",\n            \"family_info\": \"Family Information\",\n            \"employment_info\": \"Employment Information\",\n            \"financial_info\": \"Financial Information\",\n            \"marital_status\": \"Marital Status\",\n            \"single\": \"Single\",\n            \"married\": \"Married\",\n            \"divorced\": \"Divorced\",\n            \"widowed\": \"Widowed\",\n            // Assistance Requests\n            \"assistance_request\": \"Assistance Request\",\n            \"new_request\": \"New Request\",\n            \"request_type\": \"Request Type\",\n            \"requested_amount\": \"Requested Amount\",\n            \"description\": \"Description\",\n            \"attach_documents\": \"Attach Documents\",\n            \"submit_request\": \"Submit Request\",\n            // Status\n            \"draft\": \"Draft\",\n            \"submitted\": \"Submitted\",\n            // Workflow Stages\n            \"reception_review\": \"Reception Review\",\n            \"researcher_review\": \"Researcher Review\",\n            \"banking_expert_review\": \"Banking Expert Review\",\n            \"department_head_review\": \"Department Head Review\",\n            \"admin_manager_review\": \"Admin Manager Review\",\n            \"minister_review\": \"Minister Review\",\n            // Actions\n            \"approve\": \"Approve\",\n            \"reject\": \"Reject\",\n            \"return\": \"Return\",\n            \"save\": \"Save\",\n            \"edit\": \"Edit\",\n            \"delete\": \"Delete\",\n            \"view\": \"View\",\n            \"download\": \"Download\",\n            \"upload\": \"Upload\",\n            // Common\n            \"loading\": \"Loading...\",\n            \"search\": \"Search\",\n            \"filter\": \"Filter\",\n            \"date\": \"Date\",\n            \"amount\": \"Amount\",\n            \"status\": \"Status\",\n            \"notes\": \"Notes\",\n            \"documents\": \"Documents\",\n            \"history\": \"History\",\n            // Dashboard Stats\n            \"total_requests\": \"Total Requests\",\n            \"pending_review\": \"Pending Review\",\n            \"approved_today\": \"Approved Today\",\n            \"rejected_today\": \"Rejected Today\",\n            \"average_processing_days\": \"Avg. Processing Days\",\n            \"total_users\": \"Total Users\",\n            // User Roles\n            \"zakat_applicant\": \"Zakat Applicant\",\n            \"reception_staff\": \"Reception Staff\",\n            \"researcher\": \"Researcher\",\n            \"banking_expert\": \"Banking Expert\",\n            \"department_head\": \"Department Head\",\n            \"admin_manager\": \"Administration Manager\",\n            \"minister\": \"Minister\",\n            \"system_admin\": \"System Administrator\",\n            // Beneficiary Management\n            \"beneficiary_management\": \"Beneficiary Management\",\n            \"beneficiary_management_desc\": \"Manage and track Zakat and assistance beneficiaries\",\n            \"beneficiary_registration\": \"New Beneficiary Registration\",\n            \"beneficiary_registration_desc\": \"Add a new beneficiary to the Zakat management system\",\n            \"beneficiary_list\": \"Beneficiary List\",\n            \"beneficiary_profile\": \"Beneficiary Profile\",\n            \"beneficiary_details\": \"Beneficiary Details\",\n            \"beneficiary_not_found\": \"Beneficiary Not Found\",\n            \"beneficiary_not_found_desc\": \"The requested beneficiary could not be found\",\n            \"back_to_beneficiaries\": \"Back to Beneficiaries List\",\n            // Beneficiary Stats\n            \"approved_beneficiaries\": \"Approved\",\n            \"under_review_beneficiaries\": \"Under Review\",\n            \"total_distributions\": \"Total Distributions\",\n            \"average_distribution\": \"Average Distribution\",\n            \"pending_verification_count\": \"Pending Verification\",\n            \"needs_review\": \"Needs Review\",\n            \"of_total\": \"of Total\",\n            \"per_beneficiary\": \"per Beneficiary\",\n            // Search and Filters\n            \"search_and_filter\": \"Search and Filter\",\n            \"search_placeholder\": \"Search by name, national ID, or phone number...\",\n            \"filter_by_status\": \"Filter by Status\",\n            \"filter_by_category\": \"Filter by Category\",\n            \"all_statuses\": \"All Statuses\",\n            \"all_categories\": \"All Categories\",\n            \"no_results_found\": \"No results found matching search criteria\",\n            \"showing_results\": \"Showing {{count}} of {{total}} beneficiaries\",\n            \"export\": \"Export\",\n            // Beneficiary Status\n            \"pending_verification\": \"Pending Verification\",\n            \"under_review\": \"Under Review\",\n            \"approved\": \"Approved\",\n            \"rejected\": \"Rejected\",\n            \"suspended\": \"Suspended\",\n            \"inactive\": \"Inactive\",\n            // Zakat Categories\n            \"fuqara\": \"The Poor\",\n            \"masakin\": \"The Needy\",\n            \"amilin\": \"Zakat Administrators\",\n            \"muallafah\": \"Those whose hearts are reconciled\",\n            \"riqab\": \"To free slaves/captives\",\n            \"gharimin\": \"Those in debt\",\n            \"fisabilillah\": \"In the cause of Allah\",\n            \"ibnus_sabil\": \"The wayfarer/traveler\",\n            \"primary_category\": \"Primary Category\",\n            // Table Headers\n            \"name\": \"Name\",\n            \"total_received\": \"Total Received\",\n            // Actions\n            \"view_profile\": \"View Profile\",\n            \"manage_case\": \"Manage Case\",\n            \"generate_voucher\": \"Generate Voucher\",\n            \"back\": \"Back\",\n            // Profile Tabs\n            \"overview\": \"Overview\",\n            \"personal_details\": \"Personal Details\",\n            \"eligibility_verification\": \"Eligibility & Verification\",\n            \"case_management\": \"Case Management\",\n            \"distribution_history\": \"Distribution History\",\n            \"family\": \"Family\",\n            // Profile Details\n            \"eligibility_score\": \"Eligibility Score\",\n            \"high_score\": \"High Score\",\n            \"family_size\": \"Family Size\",\n            \"dependents\": \"Dependents\",\n            \"last_distribution\": \"Last Distribution\",\n            \"no_distribution\": \"None\",\n            \"not_distributed_yet\": \"Not distributed yet\",\n            // Contact Information\n            \"contact_info\": \"Contact Information\",\n            \"account_status\": \"Account Status\",\n            \"current_status\": \"Current Status\",\n            \"registration_date\": \"Registration Date\",\n            \"next_review\": \"Next Review\",\n            // Personal Information\n            \"personal_information\": \"Personal Information\",\n            \"basic_information\": \"Basic Information\",\n            \"name_arabic\": \"Name in Arabic\",\n            \"name_english\": \"Name in English\",\n            \"date_of_birth\": \"Date of Birth\",\n            \"gender\": \"Gender\",\n            \"male\": \"Male\",\n            \"female\": \"Female\",\n            // Family Members\n            \"family_members\": \"Family Members\",\n            \"no_family_info\": \"No family information available\",\n            \"dependent\": \"Dependent\",\n            \"special_needs\": \"Special Needs\",\n            \"relationship\": \"Relationship\",\n            \"age\": \"Age\",\n            \"years\": \"years\",\n            \"son\": \"Son\",\n            \"daughter\": \"Daughter\",\n            \"mother\": \"Mother\",\n            \"father\": \"Father\",\n            // Documents\n            \"no_documents\": \"No documents uploaded\",\n            \"verified\": \"Verified\",\n            \"upload_date\": \"Upload Date\",\n            // Coming Soon\n            \"coming_soon\": \"Coming Soon\",\n            \"under_development\": \"Under Development\",\n            \"registration_form_coming\": \"Registration Form Under Development\",\n            \"registration_form_desc\": \"New beneficiary registration form will be added soon\",\n            \"will_include_features\": \"The form will include the following features:\",\n            \"multi_step_form\": \"Multi-step form with progress indicator\",\n            \"dual_language_input\": \"Personal data input in Arabic and English\",\n            \"zakat_categories_selection\": \"Selection of eight Zakat categories\",\n            \"document_upload\": \"Required document upload\",\n            \"data_validation\": \"Data validation\",\n            \"duplicate_detection\": \"Automatic duplicate detection\",\n            // System Information\n            \"system_name\": \"Zakat Management System\",\n            \"system_description\": \"Comprehensive system for managing Zakat and assistance requests\",\n            // Authentication Messages\n            \"create_new_account\": \"Create new account\",\n            \"sign_in_to_account\": \"Sign in to your account\",\n            \"choose_login_method\": \"Choose the appropriate login method\",\n            \"verifying\": \"Verifying...\",\n            \"error\": \"Error\",\n            \"passwords_not_match\": \"Passwords do not match\",\n            \"account_created_success\": \"Account created successfully\",\n            \"wait_admin_approval\": \"Please wait for admin approval of your account\",\n            \"account_creation_error\": \"Error creating account\",\n            \"unexpected_error\": \"An unexpected error occurred\",\n            \"error_during_creation\": \"An error occurred during account creation\",\n            \"invalid_credentials\": \"Invalid credentials entered\",\n            \"welcome_to_system\": \"Welcome to the Zakat Management System\",\n            \"error_during_login\": \"An error occurred during login\",\n            // Account Status\n            \"account_pending_approval\": \"Your account is pending approval\",\n            \"wait_admin_approval_desc\": \"Please wait for admin approval of your account\",\n            // Dashboard\n            \"assigned_tasks\": \"tasks assigned to you\",\n            \"total_requests_desc\": \"Total requests\",\n            \"pending_review_desc\": \"Pending review\",\n            \"approved_today_desc\": \"Approved today\",\n            \"avg_processing_days_desc\": \"Average processing days\",\n            // Reports\n            \"no_reports_access\": \"You do not have permission to access reports\",\n            \"monthly_report\": \"Monthly Report\",\n            \"monthly_stats_desc\": \"Monthly statistics of requests and approvals\",\n            \"requests_label\": \"Requests\",\n            \"approved_label\": \"Approved\",\n            \"rejected_label\": \"Rejected\",\n            // Requests\n            \"back_button\": \"Back\",\n            \"request_details\": \"Request Details\",\n            \"download_decision\": \"Download Decision\",\n            // Gender and Personal Info\n            \"gender_label\": \"Gender\",\n            \"male_label\": \"Male\",\n            \"female_label\": \"Female\",\n            \"marital_status_label\": \"Marital Status\",\n            \"married_label\": \"Married\",\n            \"single_label\": \"Single\",\n            \"divorced_label\": \"Divorced\",\n            \"widowed_label\": \"Widowed\",\n            // Common UI Text\n            \"or\": \"or\",\n            \"no_account\": \"Don't have an account?\",\n            \"demo_accounts\": \"Demo Accounts:\",\n            \"applicant\": \"Applicant:\",\n            \"staff_member\": \"Staff Member:\",\n            // Access Control\n            \"access_denied\": \"Access Denied\",\n            \"no_beneficiary_access\": \"You do not have permission to access beneficiary management\",\n            \"no_registration_access\": \"You do not have permission to register new beneficiaries\"\n        }\n    }\n};\n// Initialize i18n immediately with resources\ni18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"].use(i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_2__[\"default\"]).use(react_i18next__WEBPACK_IMPORTED_MODULE_1__.initReactI18next).init({\n    resources,\n    fallbackLng: \"ar\",\n    lng: \"ar\",\n    debug: \"development\" === \"development\",\n    detection: {\n        order: [\n            \"localStorage\",\n            \"navigator\",\n            \"htmlTag\"\n        ],\n        caches: [\n            \"localStorage\"\n        ]\n    },\n    interpolation: {\n        escapeValue: false\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (i18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatDuration: () => (/* binding */ formatDuration)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDuration(seconds) {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    const remainingSeconds = seconds % 60;\n    return `${hours.toString().padStart(2, \"0\")}:${minutes.toString().padStart(2, \"0\")}:${remainingSeconds.toString().padStart(2, \"0\")}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QjtBQUVPLFNBQVNDLGVBQWVDLE9BQWU7SUFDNUMsTUFBTUMsUUFBUUMsS0FBS0MsS0FBSyxDQUFDSCxVQUFVO0lBQ25DLE1BQU1JLFVBQVVGLEtBQUtDLEtBQUssQ0FBQyxVQUFXLE9BQVE7SUFDOUMsTUFBTUUsbUJBQW1CTCxVQUFVO0lBRW5DLE9BQU8sQ0FBQyxFQUFFQyxNQUFNSyxRQUFRLEdBQUdDLFFBQVEsQ0FBQyxHQUFHLEtBQUssQ0FBQyxFQUFFSCxRQUFRRSxRQUFRLEdBQUdDLFFBQVEsQ0FBQyxHQUFHLEtBQUssQ0FBQyxFQUFFRixpQkFBaUJDLFFBQVEsR0FBR0MsUUFBUSxDQUFDLEdBQUcsS0FBSyxDQUFDO0FBQ3RJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBwLy4vbGliL3V0aWxzLnRzP2Y3NDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG4gXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0RHVyYXRpb24oc2Vjb25kczogbnVtYmVyKTogc3RyaW5nIHtcbiAgY29uc3QgaG91cnMgPSBNYXRoLmZsb29yKHNlY29uZHMgLyAzNjAwKVxuICBjb25zdCBtaW51dGVzID0gTWF0aC5mbG9vcigoc2Vjb25kcyAlIDM2MDApIC8gNjApXG4gIGNvbnN0IHJlbWFpbmluZ1NlY29uZHMgPSBzZWNvbmRzICUgNjBcblxuICByZXR1cm4gYCR7aG91cnMudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpfToke21pbnV0ZXMudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpfToke3JlbWFpbmluZ1NlY29uZHMudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpfWBcbn0iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyIsImZvcm1hdER1cmF0aW9uIiwic2Vjb25kcyIsImhvdXJzIiwiTWF0aCIsImZsb29yIiwibWludXRlcyIsInJlbWFpbmluZ1NlY29uZHMiLCJ0b1N0cmluZyIsInBhZFN0YXJ0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./providers/i18n-provider.tsx":
/*!*************************************!*\
  !*** ./providers/i18n-provider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nProvider: () => (/* binding */ I18nProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/i18n */ \"(ssr)/./lib/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ I18nProvider auto */ \n\n\nfunction I18nProvider({ children }) {\n    // Always render the I18nextProvider to avoid hydration mismatch\n    // The i18n instance is already initialized with resources in lib/i18n.ts\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_i18next__WEBPACK_IMPORTED_MODULE_1__.I18nextProvider, {\n        i18n: _lib_i18n__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\providers\\\\i18n-provider.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9wcm92aWRlcnMvaTE4bi1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBSStDO0FBQ2xCO0FBTXRCLFNBQVNFLGFBQWEsRUFBRUMsUUFBUSxFQUFxQjtJQUMxRCxnRUFBZ0U7SUFDaEUseUVBQXlFO0lBQ3pFLHFCQUNFLDhEQUFDSCwwREFBZUE7UUFBQ0MsTUFBTUEsaURBQUlBO2tCQUN4QkU7Ozs7OztBQUdQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBwLy4vcHJvdmlkZXJzL2kxOG4tcHJvdmlkZXIudHN4PzVkYjIiXSwic291cmNlc0NvbnRlbnQiOlsiXG4ndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBJMThuZXh0UHJvdmlkZXIgfSBmcm9tICdyZWFjdC1pMThuZXh0J1xuaW1wb3J0IGkxOG4gZnJvbSAnQC9saWIvaTE4bidcblxuaW50ZXJmYWNlIEkxOG5Qcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZVxufVxuXG5leHBvcnQgZnVuY3Rpb24gSTE4blByb3ZpZGVyKHsgY2hpbGRyZW4gfTogSTE4blByb3ZpZGVyUHJvcHMpIHtcbiAgLy8gQWx3YXlzIHJlbmRlciB0aGUgSTE4bmV4dFByb3ZpZGVyIHRvIGF2b2lkIGh5ZHJhdGlvbiBtaXNtYXRjaFxuICAvLyBUaGUgaTE4biBpbnN0YW5jZSBpcyBhbHJlYWR5IGluaXRpYWxpemVkIHdpdGggcmVzb3VyY2VzIGluIGxpYi9pMThuLnRzXG4gIHJldHVybiAoXG4gICAgPEkxOG5leHRQcm92aWRlciBpMThuPXtpMThufT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0kxOG5leHRQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkkxOG5leHRQcm92aWRlciIsImkxOG4iLCJJMThuUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./providers/i18n-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./providers/session-provider.tsx":
/*!****************************************!*\
  !*** ./providers/session-provider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ SessionProvider auto */ \n\nfunction SessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\providers\\\\session-provider.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9wcm92aWRlcnMvc2Vzc2lvbi1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRzRFO0FBT3JFLFNBQVNBLGdCQUFnQixFQUFFRSxRQUFRLEVBQXdCO0lBQ2hFLHFCQUFPLDhEQUFDRCw0REFBdUJBO2tCQUFFQzs7Ozs7O0FBQ25DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBwLy4vcHJvdmlkZXJzL3Nlc3Npb24tcHJvdmlkZXIudHN4PzIzNWEiXSwic291cmNlc0NvbnRlbnQiOlsiXG4ndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIGFzIE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0J1xuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5cbmludGVyZmFjZSBTZXNzaW9uUHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFNlc3Npb25Qcm92aWRlcih7IGNoaWxkcmVuIH06IFNlc3Npb25Qcm92aWRlclByb3BzKSB7XG4gIHJldHVybiA8TmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+e2NoaWxkcmVufTwvTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./providers/session-provider.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"515874a36e9a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcHAvLi9hcHAvZ2xvYmFscy5jc3M/YjcwMSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjUxNTg3NGEzNmU5YVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/auth/login/page.tsx":
/*!*********************************!*\
  !*** ./app/auth/login/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\VCode projects\zakat-deepagent\zakat_management_system\app\app\auth\login\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _providers_session_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/session-provider */ \"(rsc)/./providers/session-provider.tsx\");\n/* harmony import */ var _providers_i18n_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/i18n-provider */ \"(rsc)/./providers/i18n-provider.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"نظام إدارة الزكاة - Zakat Management System\",\n    description: \"نظام شامل لإدارة طلبات الزكاة والمساعدات\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().variable)} font-sans antialiased`,\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"light\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_session_provider__WEBPACK_IMPORTED_MODULE_2__.SessionProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_i18n_provider__WEBPACK_IMPORTED_MODULE_3__.I18nProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\VCode projects\zakat-deepagent\zakat_management_system\app\components\theme-provider.tsx#ThemeProvider`);


/***/ }),

/***/ "(rsc)/./providers/i18n-provider.tsx":
/*!*************************************!*\
  !*** ./providers/i18n-provider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   I18nProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\VCode projects\zakat-deepagent\zakat_management_system\app\providers\i18n-provider.tsx#I18nProvider`);


/***/ }),

/***/ "(rsc)/./providers/session-provider.tsx":
/*!****************************************!*\
  !*** ./providers/session-provider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SessionProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\VCode projects\zakat-deepagent\zakat_management_system\app\providers\session-provider.tsx#SessionProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@radix-ui","vendor-chunks/@floating-ui","vendor-chunks/i18next","vendor-chunks/tailwind-merge","vendor-chunks/react-i18next","vendor-chunks/lucide-react","vendor-chunks/tslib","vendor-chunks/i18next-browser-languagedetector","vendor-chunks/aria-hidden","vendor-chunks/next-themes","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/class-variance-authority","vendor-chunks/react-style-singleton","vendor-chunks/@swc","vendor-chunks/html-parse-stringify","vendor-chunks/clsx","vendor-chunks/void-elements","vendor-chunks/get-nonce"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();