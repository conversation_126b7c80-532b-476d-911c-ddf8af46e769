
'use client'

import { useState } from 'react'
import { signIn } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useSafeTranslation } from '@/hooks/use-safe-translation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { LanguageSwitcher } from '@/components/ui/language-switcher'
import { LogIn, Mail, Lock, Shield } from 'lucide-react'
import Link from 'next/link'
import { toast } from '@/hooks/use-toast'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isTawtheeqLogin, setIsTawtheeqLogin] = useState(false)
  const { t, i18n } = useSafeTranslation()
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      })

      if (result?.error) {
        toast({
          title: t('login_error'),
          description: t('invalid_credentials'),
          variant: 'destructive',
        })
      } else {
        toast({
          title: t('login_success'),
          description: t('welcome_to_system'),
        })
        router.push('/dashboard')
      }
    } catch (error) {
      toast({
        title: t('error'),
        description: t('error_during_login'),
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleTawtheeqLogin = () => {
    setIsTawtheeqLogin(true)
    // Mock Tawtheeq integration - in real app this would redirect to Tawtheeq
    setTimeout(() => {
      signIn('credentials', {
        email: '<EMAIL>',
        password: 'johndoe123',
        redirect: false,
      }).then((result) => {
        if (!result?.error) {
          router.push('/dashboard')
        }
        setIsTawtheeqLogin(false)
      })
    }, 2000)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-4">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <div className="h-16 w-16 rounded-full bg-primary flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-2xl">ز</span>
            </div>
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {t('system_name')}
            </h1>
            <p className="text-gray-600 mt-2">
              {t('sign_in_to_account')}
            </p>
          </div>
          <div className="flex justify-center">
            <LanguageSwitcher />
          </div>
        </div>

        {/* Login Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <LogIn className="h-5 w-5" />
              {t('login')}
            </CardTitle>
            <CardDescription>
              {t('choose_login_method')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Tawtheeq Login */}
            <Button
              onClick={handleTawtheeqLogin}
              disabled={isTawtheeqLogin}
              className="w-full bg-green-600 hover:bg-green-700"
            >
              {isTawtheeqLogin ? (
                <div className="flex items-center gap-2">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  {t('verifying')}
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  {t('login_tawtheeq')}
                </div>
              )}
            </Button>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">{t('or')}</span>
              </div>
            </div>

            {/* Email/Password Login */}
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">{t('email')}</Label>
                <div className="relative">
                  <Mail className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="pr-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">{t('password')}</Label>
                <div className="relative">
                  <Lock className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    className="pr-10"
                  />
                </div>
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    {t('loading')}
                  </div>
                ) : (
                  t('login')
                )}
              </Button>
            </form>

            <div className="text-center text-sm">
              <span className="text-muted-foreground">{t('no_account')} </span>
              <Link href="/auth/register" className="text-primary hover:underline">
                {t('register')}
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Demo Credentials */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="pt-6">
            <div className="text-center space-y-2">
              <p className="text-sm font-medium text-blue-900">
                {t('demo_accounts')}
              </p>
              <div className="text-xs text-blue-700 space-y-1">
                <p><strong>{t('system_admin')}</strong> <EMAIL> / johndoe123</p>
                <p><strong>{t('applicant')}</strong> <EMAIL> / johndoe123</p>
                <p><strong>{t('staff_member')}</strong> <EMAIL> / johndoe123</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
