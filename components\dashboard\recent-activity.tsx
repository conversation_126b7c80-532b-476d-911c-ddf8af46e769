'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useTranslation } from 'react-i18next'
import { formatDistanceToNow } from 'date-fns'
import { ar, enUS } from 'date-fns/locale'
import { 
  UserPlus, 
  FileCheck, 
  CreditCard, 
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react'

interface ActivityItem {
  id: string
  type: 'registration' | 'approval' | 'distribution' | 'review'
  title: string
  description: string
  timestamp: Date
  status: 'completed' | 'pending' | 'warning'
  user?: string
}

// Mock data - replace with real data from your mock-data.ts
const mockActivities: ActivityItem[] = [
  {
    id: '1',
    type: 'registration',
    title: 'New beneficiary registered',
    description: '<PERSON> has been registered',
    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    status: 'completed',
    user: 'Reception Staff'
  },
  {
    id: '2',
    type: 'approval',
    title: 'Application approved',
    description: 'Fatima Al-Zahra application approved for Zakat distribution',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    status: 'completed',
    user: 'Case Manager'
  },
  {
    id: '3',
    type: 'distribution',
    title: 'Zakat distributed',
    description: '5,000 SAR distributed to 10 beneficiaries',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
    status: 'completed',
    user: 'Finance Manager'
  },
  {
    id: '4',
    type: 'review',
    title: 'Pending review',
    description: '3 applications require case manager review',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
    status: 'pending',
    user: 'System'
  }
]

export function RecentActivity() {
  const { t, i18n } = useTranslation()
  const isArabic = i18n.language === 'ar'

  const getActivityIcon = (type: ActivityItem['type']) => {
    switch (type) {
      case 'registration': return UserPlus
      case 'approval': return CheckCircle
      case 'distribution': return CreditCard
      case 'review': return AlertCircle
      default: return Clock
    }
  }

  const getStatusColor = (status: ActivityItem['status']) => {
    switch (status) {
      case 'completed': return 'default'
      case 'pending': return 'secondary'
      case 'warning': return 'destructive'
      default: return 'secondary'
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>{t('recent_activity')}</CardTitle>
        <Button variant="ghost" size="sm">
          {t('view_all')}
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {mockActivities.map((activity) => {
            const Icon = getActivityIcon(activity.type)
            return (
              <div key={activity.id} className="flex items-start space-x-3 rtl:space-x-reverse">
                <div className="flex-shrink-0">
                  <Icon className="h-5 w-5 text-muted-foreground" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium">{activity.title}</p>
                    <Badge variant={getStatusColor(activity.status)} className="text-xs">
                      {t(activity.status)}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">{activity.description}</p>
                  <p className="text-xs text-muted-foreground">
                    {formatDistanceToNow(activity.timestamp, { 
                      addSuffix: true,
                      locale: isArabic ? ar : enUS
                    })}
                    {activity.user && ` • ${activity.user}`}
                  </p>
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}