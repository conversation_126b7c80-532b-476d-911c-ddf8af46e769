"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_ivahr_OneDrive_Documents_VCode_projects_zakat_deepagent_zakat_management_system_app_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\api\\\\auth\\\\[...nextauth]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_ivahr_OneDrive_Documents_VCode_projects_zakat_deepagent_zakat_management_system_app_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/[...nextauth]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/auth/[...nextauth]/route.ts":
/*!*********************************************!*\
  !*** ./app/api/auth/[...nextauth]/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _lib_mock_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/mock-data */ \"(rsc)/./lib/mock-data.ts\");\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                // Find user in mock data\n                const user = (0,_lib_mock_data__WEBPACK_IMPORTED_MODULE_2__.getUserByEmail)(credentials.email);\n                if (!user) {\n                    return null;\n                }\n                // For demo purposes, we'll use simple password check\n                // In production, this should be properly hashed and compared\n                const isValidPassword = credentials.password === \"johndoe123\" || credentials.email === \"<EMAIL>\";\n                if (!isValidPassword) {\n                    return null;\n                }\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.fullName,\n                    role: user.role,\n                    nationalId: user.nationalId,\n                    accountStatus: user.accountStatus\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.nationalId = user.nationalId;\n                token.accountStatus = user.accountStatus;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.nationalId = token.nationalId;\n                session.user.accountStatus = token.accountStatus;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/login\",\n        error: \"/auth/error\"\n    }\n};\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/mock-data.ts":
/*!**************************!*\
  !*** ./lib/mock-data.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBeneficiariesByCategory: () => (/* binding */ getBeneficiariesByCategory),\n/* harmony export */   getBeneficiariesByStatus: () => (/* binding */ getBeneficiariesByStatus),\n/* harmony export */   getBeneficiaryById: () => (/* binding */ getBeneficiaryById),\n/* harmony export */   getBeneficiaryStats: () => (/* binding */ getBeneficiaryStats),\n/* harmony export */   getRequestsByUserId: () => (/* binding */ getRequestsByUserId),\n/* harmony export */   getTasksByUserId: () => (/* binding */ getTasksByUserId),\n/* harmony export */   getUserByEmail: () => (/* binding */ getUserByEmail),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   mockAssistanceRequests: () => (/* binding */ mockAssistanceRequests),\n/* harmony export */   mockAssistanceTypes: () => (/* binding */ mockAssistanceTypes),\n/* harmony export */   mockBeneficiaries: () => (/* binding */ mockBeneficiaries),\n/* harmony export */   mockDashboardStats: () => (/* binding */ mockDashboardStats),\n/* harmony export */   mockTasks: () => (/* binding */ mockTasks),\n/* harmony export */   mockUsers: () => (/* binding */ mockUsers),\n/* harmony export */   searchBeneficiaries: () => (/* binding */ searchBeneficiaries),\n/* harmony export */   zakatCategoryLabels: () => (/* binding */ zakatCategoryLabels)\n/* harmony export */ });\n// Mock Users for each role\nconst mockUsers = [\n    // Test admin account\n    {\n        id: \"1\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"John Doe\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1980-01-01\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"system_admin\",\n        createdAt: new Date(\"2023-01-01\"),\n        lastLogin: new Date()\n    },\n    // Zakat Applicants\n    {\n        id: \"2\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Ahmed Salem Al-Rashid\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1985-05-15\"),\n        phoneNumber: \"+************\",\n        address: \"Jeddah, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"zakat_applicant\",\n        createdAt: new Date(\"2023-02-01\"),\n        lastLogin: new Date(),\n        profileId: \"profile-2\"\n    },\n    {\n        id: \"3\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Fatima Omar Al-Zahra\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1990-08-20\"),\n        phoneNumber: \"+************\",\n        address: \"Dammam, Saudi Arabia\",\n        accountStatus: \"pending_approval\",\n        role: \"zakat_applicant\",\n        createdAt: new Date(\"2023-03-01\"),\n        profileId: \"profile-3\"\n    },\n    // Staff Members\n    {\n        id: \"4\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Sara Abdullah Al-Mansouri\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1988-03-10\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"reception_staff\",\n        createdAt: new Date(\"2022-01-15\"),\n        lastLogin: new Date()\n    },\n    {\n        id: \"5\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Mohammed Hassan Al-Qadiri\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1982-07-25\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"researcher\",\n        createdAt: new Date(\"2022-02-01\"),\n        lastLogin: new Date()\n    },\n    {\n        id: \"6\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Khalid Ahmed Al-Othman\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1975-12-05\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"banking_expert\",\n        createdAt: new Date(\"2021-01-01\"),\n        lastLogin: new Date()\n    },\n    {\n        id: \"7\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Nasser Fahad Al-Saud\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1970-04-18\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"department_head\",\n        createdAt: new Date(\"2020-01-01\"),\n        lastLogin: new Date()\n    },\n    {\n        id: \"8\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Abdulaziz Mohammed Al-Rashid\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1968-09-12\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"admin_manager\",\n        createdAt: new Date(\"2019-01-01\"),\n        lastLogin: new Date()\n    },\n    {\n        id: \"9\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"His Excellency Abdullah bin Salman\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1965-02-28\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"minister\",\n        createdAt: new Date(\"2018-01-01\"),\n        lastLogin: new Date()\n    }\n];\n// Mock Assistance Types\nconst mockAssistanceTypes = [\n    {\n        id: \"financial-support\",\n        nameAr: \"المساعدة المالية العامة\",\n        nameEn: \"General Financial Support\",\n        descriptionAr: \"مساعدة مالية للأسر المحتاجة\",\n        descriptionEn: \"Financial assistance for needy families\",\n        maxAmount: 25000,\n        requiredDocuments: [\n            {\n                id: \"salary-certificate\",\n                nameAr: \"شهادة راتب\",\n                nameEn: \"Salary Certificate\",\n                isRequired: true,\n                acceptedFormats: [\n                    \"pdf\",\n                    \"jpg\",\n                    \"png\"\n                ],\n                maxSizeKB: 2048\n            },\n            {\n                id: \"bank-statement\",\n                nameAr: \"كشف حساب بنكي\",\n                nameEn: \"Bank Statement\",\n                isRequired: true,\n                acceptedFormats: [\n                    \"pdf\"\n                ],\n                maxSizeKB: 5120\n            }\n        ],\n        eligibilityCriteria: [\n            {\n                field: \"monthlyIncome\",\n                condition: \"less_than\",\n                value: 5000,\n                nationality: \"Saudi Arabia\"\n            }\n        ],\n        isActive: true,\n        category: \"Financial\"\n    },\n    {\n        id: \"medical-support\",\n        nameAr: \"المساعدة الطبية\",\n        nameEn: \"Medical Support\",\n        descriptionAr: \"مساعدة لتغطية التكاليف الطبية\",\n        descriptionEn: \"Assistance to cover medical expenses\",\n        maxAmount: 50000,\n        requiredDocuments: [\n            {\n                id: \"medical-report\",\n                nameAr: \"التقرير الطبي\",\n                nameEn: \"Medical Report\",\n                isRequired: true,\n                acceptedFormats: [\n                    \"pdf\",\n                    \"jpg\",\n                    \"png\"\n                ],\n                maxSizeKB: 3072\n            },\n            {\n                id: \"medical-bills\",\n                nameAr: \"الفواتير الطبية\",\n                nameEn: \"Medical Bills\",\n                isRequired: true,\n                acceptedFormats: [\n                    \"pdf\",\n                    \"jpg\",\n                    \"png\"\n                ],\n                maxSizeKB: 5120\n            }\n        ],\n        eligibilityCriteria: [],\n        isActive: true,\n        category: \"Medical\"\n    },\n    {\n        id: \"education-support\",\n        nameAr: \"المساعدة التعليمية\",\n        nameEn: \"Education Support\",\n        descriptionAr: \"مساعدة لتغطية تكاليف التعليم\",\n        descriptionEn: \"Assistance to cover education expenses\",\n        maxAmount: 15000,\n        requiredDocuments: [\n            {\n                id: \"enrollment-certificate\",\n                nameAr: \"شهادة قيد\",\n                nameEn: \"Enrollment Certificate\",\n                isRequired: true,\n                acceptedFormats: [\n                    \"pdf\",\n                    \"jpg\",\n                    \"png\"\n                ],\n                maxSizeKB: 2048\n            }\n        ],\n        eligibilityCriteria: [\n            {\n                field: \"familyMembersCount\",\n                condition: \"greater_than\",\n                value: 0\n            }\n        ],\n        isActive: true,\n        category: \"Education\"\n    }\n];\n// Mock Assistance Requests\nconst mockAssistanceRequests = [\n    {\n        id: \"req-001\",\n        userId: \"2\",\n        assistanceType: mockAssistanceTypes[0],\n        requestedAmount: 15000,\n        approvedAmount: 12000,\n        description: \"نحتاج إلى مساعدة مالية لتغطية تكاليف المعيشة بعد توقف العمل\",\n        status: \"approved\",\n        submissionDate: new Date(\"2023-10-15\"),\n        lastUpdateDate: new Date(\"2023-11-01\"),\n        attachedDocuments: [],\n        workflow: [\n            {\n                id: \"step-1\",\n                requestId: \"req-001\",\n                stage: \"reception_review\",\n                reviewerId: \"4\",\n                reviewerName: \"Sara Abdullah Al-Mansouri\",\n                stageStartDate: new Date(\"2023-10-15\"),\n                stageEndDate: new Date(\"2023-10-16\"),\n                decision: \"approve\",\n                decisionDate: new Date(\"2023-10-16\"),\n                notes: \"المستندات كاملة والحالة تستدعي المساعدة\"\n            },\n            {\n                id: \"step-2\",\n                requestId: \"req-001\",\n                stage: \"approved\",\n                reviewerId: \"8\",\n                reviewerName: \"Abdulaziz Mohammed Al-Rashid\",\n                stageStartDate: new Date(\"2023-10-30\"),\n                stageEndDate: new Date(\"2023-11-01\"),\n                decision: \"approve\",\n                decisionDate: new Date(\"2023-11-01\"),\n                notes: \"تمت الموافقة على مبلغ 12,000 ريال\"\n            }\n        ],\n        priority: \"medium\"\n    },\n    {\n        id: \"req-002\",\n        userId: \"3\",\n        assistanceType: mockAssistanceTypes[1],\n        requestedAmount: 35000,\n        description: \"نحتاج مساعدة لتغطية تكاليف علاج والدي في المستشفى\",\n        status: \"researcher_review\",\n        submissionDate: new Date(\"2023-11-10\"),\n        lastUpdateDate: new Date(\"2023-11-12\"),\n        attachedDocuments: [],\n        workflow: [\n            {\n                id: \"step-1\",\n                requestId: \"req-002\",\n                stage: \"reception_review\",\n                reviewerId: \"4\",\n                reviewerName: \"Sara Abdullah Al-Mansouri\",\n                stageStartDate: new Date(\"2023-11-10\"),\n                stageEndDate: new Date(\"2023-11-11\"),\n                decision: \"approve\",\n                decisionDate: new Date(\"2023-11-11\"),\n                notes: \"تم مراجعة الطلب وإحالته للباحث\"\n            },\n            {\n                id: \"step-2\",\n                requestId: \"req-002\",\n                stage: \"researcher_review\",\n                reviewerId: \"5\",\n                reviewerName: \"Mohammed Hassan Al-Qadiri\",\n                stageStartDate: new Date(\"2023-11-11\"),\n                notes: \"قيد المراجعة والتحقق من المستندات الطبية\"\n            }\n        ],\n        priority: \"high\"\n    }\n];\n// Mock Tasks for different roles\nconst mockTasks = {\n    reception_staff: [\n        {\n            id: \"task-1\",\n            assignedTo: \"4\",\n            requestId: \"req-003\",\n            type: \"profile_review\",\n            priority: \"medium\",\n            dueDate: new Date(Date.now() + ********),\n            status: \"pending\",\n            createdDate: new Date()\n        },\n        {\n            id: \"task-2\",\n            assignedTo: \"4\",\n            requestId: \"req-004\",\n            type: \"request_review\",\n            priority: \"high\",\n            status: \"in_progress\",\n            createdDate: new Date(Date.now() - ********)\n        }\n    ],\n    researcher: [\n        {\n            id: \"task-3\",\n            assignedTo: \"5\",\n            requestId: \"req-002\",\n            type: \"request_review\",\n            priority: \"high\",\n            dueDate: new Date(Date.now() + *********),\n            status: \"in_progress\",\n            createdDate: new Date(\"2023-11-11\")\n        }\n    ],\n    banking_expert: [\n        {\n            id: \"task-4\",\n            assignedTo: \"6\",\n            requestId: \"req-005\",\n            type: \"request_review\",\n            priority: \"medium\",\n            dueDate: new Date(Date.now() + *********),\n            status: \"pending\",\n            createdDate: new Date()\n        }\n    ],\n    department_head: [],\n    admin_manager: [],\n    minister: [],\n    zakat_applicant: [],\n    system_admin: []\n};\n// Mock Dashboard Stats for different roles\nconst mockDashboardStats = {\n    reception_staff: {\n        totalRequests: 45,\n        pendingReview: 8,\n        approvedToday: 3,\n        rejectedToday: 1,\n        averageProcessingDays: 2,\n        totalUsers: 150\n    },\n    researcher: {\n        totalRequests: 32,\n        pendingReview: 5,\n        approvedToday: 2,\n        rejectedToday: 0,\n        averageProcessingDays: 3,\n        totalUsers: 150\n    },\n    banking_expert: {\n        totalRequests: 28,\n        pendingReview: 4,\n        approvedToday: 1,\n        rejectedToday: 1,\n        averageProcessingDays: 4,\n        totalUsers: 150\n    },\n    department_head: {\n        totalRequests: 15,\n        pendingReview: 2,\n        approvedToday: 1,\n        rejectedToday: 0,\n        averageProcessingDays: 5,\n        totalUsers: 150\n    },\n    admin_manager: {\n        totalRequests: 120,\n        pendingReview: 8,\n        approvedToday: 5,\n        rejectedToday: 2,\n        averageProcessingDays: 12,\n        totalUsers: 150\n    },\n    minister: {\n        totalRequests: 8,\n        pendingReview: 1,\n        approvedToday: 0,\n        rejectedToday: 0,\n        averageProcessingDays: 7,\n        totalUsers: 150\n    },\n    zakat_applicant: {\n        totalRequests: 3,\n        pendingReview: 1,\n        approvedToday: 0,\n        rejectedToday: 0,\n        averageProcessingDays: 14,\n        totalUsers: 1\n    },\n    system_admin: {\n        totalRequests: 200,\n        pendingReview: 25,\n        approvedToday: 12,\n        rejectedToday: 3,\n        averageProcessingDays: 10,\n        totalUsers: 150\n    }\n};\n// Helper function to get user by ID\nconst getUserById = (id)=>{\n    return mockUsers.find((user)=>user.id === id);\n};\n// Helper function to get user by email\nconst getUserByEmail = (email)=>{\n    return mockUsers.find((user)=>user.email === email);\n};\n// Helper function to get requests by user ID\nconst getRequestsByUserId = (userId)=>{\n    return mockAssistanceRequests.filter((request)=>request.userId === userId);\n};\n// Helper function to get tasks by user ID\nconst getTasksByUserId = (userId)=>{\n    const user = getUserById(userId);\n    if (!user) return [];\n    return mockTasks[user.role] || [];\n};\n// Zakat Category Labels\nconst zakatCategoryLabels = {\n    fuqara: {\n        ar: \"الفقراء\",\n        en: \"The Poor\"\n    },\n    masakin: {\n        ar: \"المساكين\",\n        en: \"The Needy\"\n    },\n    amilin: {\n        ar: \"العاملين عليها\",\n        en: \"Zakat Administrators\"\n    },\n    muallafah: {\n        ar: \"المؤلفة قلوبهم\",\n        en: \"Those whose hearts are reconciled\"\n    },\n    riqab: {\n        ar: \"في الرقاب\",\n        en: \"To free slaves/captives\"\n    },\n    gharimin: {\n        ar: \"الغارمين\",\n        en: \"Those in debt\"\n    },\n    fisabilillah: {\n        ar: \"في سبيل الله\",\n        en: \"In the cause of Allah\"\n    },\n    ibnus_sabil: {\n        ar: \"ابن السبيل\",\n        en: \"The wayfarer/traveler\"\n    }\n};\n// Mock Beneficiaries Data\nconst mockBeneficiaries = [\n    {\n        id: \"ben-001\",\n        fullNameAr: \"أحمد محمد العبدالله\",\n        fullNameEn: \"Ahmed Mohammed Al-Abdullah\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1985-03-15\"),\n        gender: \"male\",\n        maritalStatus: \"married\",\n        phoneNumber: \"+966501234567\",\n        email: \"<EMAIL>\",\n        address: \"حي النهضة، شارع الملك فهد\",\n        city: \"الرياض\",\n        region: \"منطقة الرياض\",\n        postalCode: \"12345\",\n        zakatCategories: [\n            \"fuqara\",\n            \"gharimin\"\n        ],\n        primaryCategory: \"fuqara\",\n        eligibilityScore: 85,\n        monthlyIncome: 2500,\n        familySize: 5,\n        dependents: 3,\n        status: \"approved\",\n        verificationStatus: \"completed\",\n        registrationDate: new Date(\"2024-01-15\"),\n        lastVerificationDate: new Date(\"2024-01-20\"),\n        nextReviewDate: new Date(\"2024-07-15\"),\n        caseId: \"case-001\",\n        assignedStaffId: \"4\",\n        priority: \"medium\",\n        totalReceived: 15000,\n        lastDistributionDate: new Date(\"2024-01-25\"),\n        distributionCount: 3,\n        documents: [\n            {\n                id: \"doc-001\",\n                type: \"national_id\",\n                name: \"National ID Copy\",\n                uploadDate: new Date(\"2024-01-15\"),\n                verified: true,\n                verifiedBy: \"4\",\n                verifiedAt: new Date(\"2024-01-16\"),\n                fileSize: 1024000,\n                mimeType: \"image/jpeg\"\n            }\n        ],\n        createdBy: \"4\",\n        createdAt: new Date(\"2024-01-15\"),\n        updatedAt: new Date(\"2024-01-25\"),\n        notes: \"Family breadwinner, needs regular support\"\n    },\n    {\n        id: \"ben-002\",\n        fullNameAr: \"فاطمة علي الزهراء\",\n        fullNameEn: \"Fatima Ali Al-Zahra\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1990-07-22\"),\n        gender: \"female\",\n        maritalStatus: \"widowed\",\n        phoneNumber: \"+966502345678\",\n        email: \"<EMAIL>\",\n        address: \"حي الملز، شارع العليا\",\n        city: \"الرياض\",\n        region: \"منطقة الرياض\",\n        zakatCategories: [\n            \"masakin\",\n            \"fuqara\"\n        ],\n        primaryCategory: \"masakin\",\n        eligibilityScore: 92,\n        monthlyIncome: 1200,\n        familySize: 4,\n        dependents: 3,\n        status: \"approved\",\n        verificationStatus: \"completed\",\n        registrationDate: new Date(\"2024-02-01\"),\n        lastVerificationDate: new Date(\"2024-02-05\"),\n        nextReviewDate: new Date(\"2024-08-01\"),\n        caseId: \"case-002\",\n        assignedStaffId: \"5\",\n        priority: \"high\",\n        totalReceived: 22000,\n        lastDistributionDate: new Date(\"2024-02-10\"),\n        distributionCount: 4,\n        familyMembers: [\n            {\n                id: \"fam-001\",\n                name: \"محمد علي الزهراء\",\n                relationship: \"son\",\n                age: 12,\n                isDependent: true,\n                hasSpecialNeeds: false,\n                hasDisability: false\n            },\n            {\n                id: \"fam-002\",\n                name: \"عائشة علي الزهراء\",\n                relationship: \"daughter\",\n                age: 8,\n                isDependent: true,\n                hasSpecialNeeds: true,\n                hasDisability: true\n            }\n        ],\n        documents: [\n            {\n                id: \"doc-002\",\n                type: \"family_card\",\n                name: \"Family Registration Card\",\n                uploadDate: new Date(\"2024-02-01\"),\n                verified: true,\n                verifiedBy: \"5\",\n                verifiedAt: new Date(\"2024-02-02\"),\n                fileSize: 2048000,\n                mimeType: \"application/pdf\"\n            }\n        ],\n        createdBy: \"5\",\n        createdAt: new Date(\"2024-02-01\"),\n        updatedAt: new Date(\"2024-02-10\"),\n        notes: \"Widow with special needs child, priority case\"\n    },\n    {\n        id: \"ben-003\",\n        fullNameAr: \"خالد سعد الغامدي\",\n        fullNameEn: \"Khalid Saad Al-Ghamdi\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1978-11-10\"),\n        gender: \"male\",\n        maritalStatus: \"married\",\n        phoneNumber: \"+966503456789\",\n        address: \"حي الشفا، طريق الملك عبدالعزيز\",\n        city: \"جدة\",\n        region: \"منطقة مكة المكرمة\",\n        zakatCategories: [\n            \"gharimin\",\n            \"fuqara\"\n        ],\n        primaryCategory: \"gharimin\",\n        eligibilityScore: 78,\n        monthlyIncome: 3200,\n        familySize: 6,\n        dependents: 4,\n        status: \"under_review\",\n        verificationStatus: \"in_progress\",\n        registrationDate: new Date(\"2024-02-15\"),\n        caseId: \"case-003\",\n        assignedStaffId: \"6\",\n        priority: \"medium\",\n        totalReceived: 8000,\n        lastDistributionDate: new Date(\"2024-01-10\"),\n        distributionCount: 2,\n        documents: [\n            {\n                id: \"doc-003\",\n                type: \"income_certificate\",\n                name: \"Income Certificate\",\n                uploadDate: new Date(\"2024-02-15\"),\n                verified: false,\n                fileSize: 1536000,\n                mimeType: \"application/pdf\"\n            }\n        ],\n        createdBy: \"6\",\n        createdAt: new Date(\"2024-02-15\"),\n        updatedAt: new Date(\"2024-02-16\"),\n        notes: \"Business owner facing financial difficulties\"\n    },\n    {\n        id: \"ben-004\",\n        fullNameAr: \"مريم عبدالرحمن القحطاني\",\n        fullNameEn: \"Maryam Abdulrahman Al-Qahtani\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1995-04-18\"),\n        gender: \"female\",\n        maritalStatus: \"single\",\n        phoneNumber: \"+966504567890\",\n        address: \"حي الروضة، شارع التحلية\",\n        city: \"الدمام\",\n        region: \"المنطقة الشرقية\",\n        zakatCategories: [\n            \"ibnus_sabil\"\n        ],\n        primaryCategory: \"ibnus_sabil\",\n        eligibilityScore: 65,\n        monthlyIncome: 0,\n        familySize: 1,\n        dependents: 0,\n        status: \"pending_verification\",\n        verificationStatus: \"pending\",\n        registrationDate: new Date(\"2024-02-20\"),\n        priority: \"low\",\n        totalReceived: 0,\n        distributionCount: 0,\n        documents: [\n            {\n                id: \"doc-004\",\n                type: \"national_id\",\n                name: \"National ID Copy\",\n                uploadDate: new Date(\"2024-02-20\"),\n                verified: false,\n                fileSize: 896000,\n                mimeType: \"image/png\"\n            }\n        ],\n        createdBy: \"4\",\n        createdAt: new Date(\"2024-02-20\"),\n        updatedAt: new Date(\"2024-02-20\"),\n        notes: \"Student seeking temporary assistance\"\n    },\n    {\n        id: \"ben-005\",\n        fullNameAr: \"عبدالله يوسف الشهري\",\n        fullNameEn: \"Abdullah Yusuf Al-Shehri\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1982-09-05\"),\n        gender: \"male\",\n        maritalStatus: \"divorced\",\n        phoneNumber: \"+966505678901\",\n        address: \"حي العزيزية، شارع الأمير سلطان\",\n        city: \"الطائف\",\n        region: \"منطقة مكة المكرمة\",\n        zakatCategories: [\n            \"fuqara\",\n            \"gharimin\"\n        ],\n        primaryCategory: \"fuqara\",\n        eligibilityScore: 88,\n        monthlyIncome: 1800,\n        familySize: 3,\n        dependents: 2,\n        status: \"approved\",\n        verificationStatus: \"completed\",\n        registrationDate: new Date(\"2024-01-05\"),\n        lastVerificationDate: new Date(\"2024-01-10\"),\n        nextReviewDate: new Date(\"2024-07-05\"),\n        caseId: \"case-005\",\n        assignedStaffId: \"4\",\n        priority: \"medium\",\n        totalReceived: 18500,\n        lastDistributionDate: new Date(\"2024-02-01\"),\n        distributionCount: 5,\n        familyMembers: [\n            {\n                id: \"fam-003\",\n                name: \"سارة عبدالله الشهري\",\n                relationship: \"daughter\",\n                age: 10,\n                isDependent: true,\n                hasSpecialNeeds: false,\n                hasDisability: false\n            },\n            {\n                id: \"fam-004\",\n                name: \"عمر عبدالله الشهري\",\n                relationship: \"son\",\n                age: 7,\n                isDependent: true,\n                hasSpecialNeeds: false,\n                hasDisability: false\n            }\n        ],\n        documents: [\n            {\n                id: \"doc-005\",\n                type: \"family_card\",\n                name: \"Family Registration Card\",\n                uploadDate: new Date(\"2024-01-05\"),\n                verified: true,\n                verifiedBy: \"4\",\n                verifiedAt: new Date(\"2024-01-06\"),\n                fileSize: 1792000,\n                mimeType: \"application/pdf\"\n            }\n        ],\n        createdBy: \"4\",\n        createdAt: new Date(\"2024-01-05\"),\n        updatedAt: new Date(\"2024-02-01\"),\n        notes: \"Divorced father with custody of children\"\n    },\n    {\n        id: \"ben-006\",\n        fullNameAr: \"نورا أحمد الحربي\",\n        fullNameEn: \"Nora Ahmed Al-Harbi\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1988-12-03\"),\n        gender: \"female\",\n        maritalStatus: \"married\",\n        phoneNumber: \"+966506789012\",\n        email: \"<EMAIL>\",\n        address: \"حي الملقا، شارع الأمير محمد بن عبدالعزيز\",\n        city: \"الرياض\",\n        region: \"منطقة الرياض\",\n        zakatCategories: [\n            \"masakin\",\n            \"fuqara\"\n        ],\n        primaryCategory: \"masakin\",\n        eligibilityScore: 91,\n        monthlyIncome: 1500,\n        familySize: 7,\n        dependents: 5,\n        status: \"approved\",\n        verificationStatus: \"completed\",\n        registrationDate: new Date(\"2024-01-20\"),\n        lastVerificationDate: new Date(\"2024-01-25\"),\n        nextReviewDate: new Date(\"2024-07-20\"),\n        caseId: \"case-006\",\n        assignedStaffId: \"5\",\n        priority: \"high\",\n        totalReceived: 25000,\n        lastDistributionDate: new Date(\"2024-02-15\"),\n        distributionCount: 6,\n        documents: [\n            {\n                id: \"doc-006\",\n                type: \"family_card\",\n                name: \"Family Registration Card\",\n                uploadDate: new Date(\"2024-01-20\"),\n                verified: true,\n                verifiedBy: \"5\",\n                verifiedAt: new Date(\"2024-01-21\"),\n                fileSize: 2304000,\n                mimeType: \"application/pdf\"\n            }\n        ],\n        createdBy: \"5\",\n        createdAt: new Date(\"2024-01-20\"),\n        updatedAt: new Date(\"2024-02-15\"),\n        notes: \"Large family with multiple dependents\"\n    },\n    {\n        id: \"ben-007\",\n        fullNameAr: \"محمد عبدالله الدوسري\",\n        fullNameEn: \"Mohammed Abdullah Al-Dosari\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1975-06-14\"),\n        gender: \"male\",\n        maritalStatus: \"married\",\n        phoneNumber: \"+966507890123\",\n        address: \"حي الفيصلية، طريق الملك فهد\",\n        city: \"الدمام\",\n        region: \"المنطقة الشرقية\",\n        zakatCategories: [\n            \"gharimin\",\n            \"fuqara\"\n        ],\n        primaryCategory: \"gharimin\",\n        eligibilityScore: 82,\n        monthlyIncome: 2800,\n        familySize: 4,\n        dependents: 2,\n        status: \"approved\",\n        verificationStatus: \"completed\",\n        registrationDate: new Date(\"2023-12-10\"),\n        lastVerificationDate: new Date(\"2023-12-15\"),\n        nextReviewDate: new Date(\"2024-06-10\"),\n        caseId: \"case-007\",\n        assignedStaffId: \"6\",\n        priority: \"medium\",\n        totalReceived: 12000,\n        lastDistributionDate: new Date(\"2024-01-30\"),\n        distributionCount: 4,\n        documents: [\n            {\n                id: \"doc-007\",\n                type: \"income_certificate\",\n                name: \"Income Certificate\",\n                uploadDate: new Date(\"2023-12-10\"),\n                verified: true,\n                verifiedBy: \"6\",\n                verifiedAt: new Date(\"2023-12-11\"),\n                fileSize: 1280000,\n                mimeType: \"application/pdf\"\n            }\n        ],\n        createdBy: \"6\",\n        createdAt: new Date(\"2023-12-10\"),\n        updatedAt: new Date(\"2024-01-30\"),\n        notes: \"Small business owner with debt issues\"\n    },\n    {\n        id: \"ben-008\",\n        fullNameAr: \"عائشة سالم القرني\",\n        fullNameEn: \"Aisha Salem Al-Qarni\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1992-08-27\"),\n        gender: \"female\",\n        maritalStatus: \"single\",\n        phoneNumber: \"+966508901234\",\n        address: \"حي الشرفية، شارع الستين\",\n        city: \"جدة\",\n        region: \"منطقة مكة المكرمة\",\n        zakatCategories: [\n            \"fuqara\"\n        ],\n        primaryCategory: \"fuqara\",\n        eligibilityScore: 75,\n        monthlyIncome: 1000,\n        familySize: 2,\n        dependents: 1,\n        status: \"under_review\",\n        verificationStatus: \"in_progress\",\n        registrationDate: new Date(\"2024-02-25\"),\n        caseId: \"case-008\",\n        assignedStaffId: \"4\",\n        priority: \"medium\",\n        totalReceived: 3000,\n        lastDistributionDate: new Date(\"2024-01-15\"),\n        distributionCount: 1,\n        familyMembers: [\n            {\n                id: \"fam-005\",\n                name: \"فاطمة سالم القرني\",\n                relationship: \"mother\",\n                age: 65,\n                isDependent: true,\n                hasSpecialNeeds: true,\n                hasDisability: true\n            }\n        ],\n        documents: [\n            {\n                id: \"doc-008\",\n                type: \"medical_report\",\n                name: \"Medical Report for Mother\",\n                uploadDate: new Date(\"2024-02-25\"),\n                verified: false,\n                fileSize: 3072000,\n                mimeType: \"application/pdf\"\n            }\n        ],\n        createdBy: \"4\",\n        createdAt: new Date(\"2024-02-25\"),\n        updatedAt: new Date(\"2024-02-26\"),\n        notes: \"Caring for elderly mother with medical needs\"\n    }\n];\n// Helper functions for beneficiaries\nconst getBeneficiaryById = (id)=>{\n    return mockBeneficiaries.find((beneficiary)=>beneficiary.id === id);\n};\nconst getBeneficiariesByStatus = (status)=>{\n    return mockBeneficiaries.filter((beneficiary)=>beneficiary.status === status);\n};\nconst getBeneficiariesByCategory = (category)=>{\n    return mockBeneficiaries.filter((beneficiary)=>beneficiary.zakatCategories.includes(category) || beneficiary.primaryCategory === category);\n};\nconst searchBeneficiaries = (searchTerm)=>{\n    const term = searchTerm.toLowerCase();\n    return mockBeneficiaries.filter((beneficiary)=>beneficiary.fullNameAr.toLowerCase().includes(term) || beneficiary.fullNameEn.toLowerCase().includes(term) || beneficiary.nationalId.includes(term) || beneficiary.phoneNumber.includes(term) || beneficiary.email?.toLowerCase().includes(term));\n};\nconst getBeneficiaryStats = ()=>{\n    const total = mockBeneficiaries.length;\n    const approved = mockBeneficiaries.filter((b)=>b.status === \"approved\").length;\n    const pending = mockBeneficiaries.filter((b)=>b.status === \"pending_verification\").length;\n    const underReview = mockBeneficiaries.filter((b)=>b.status === \"under_review\").length;\n    const totalDistributed = mockBeneficiaries.reduce((sum, b)=>sum + b.totalReceived, 0);\n    return {\n        total,\n        approved,\n        pending,\n        underReview,\n        totalDistributed,\n        averageDistribution: total > 0 ? Math.round(totalDistributed / total) : 0\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/mock-data.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/uuid","vendor-chunks/oauth","vendor-chunks/@panva","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/preact","vendor-chunks/oidc-token-hash","vendor-chunks/object-hash","vendor-chunks/lru-cache"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();