'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { useTranslation } from 'react-i18next'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { 
  UserPlus, 
  Search, 
  FileText, 
  Settings,
  Users,
  CreditCard
} from 'lucide-react'

export function QuickActions() {
  const { t } = useTranslation()
  const { data: session } = useSession()
  const router = useRouter()

  const actions = [
    {
      title: t('register_new_beneficiary'),
      description: 'Add a new beneficiary to the system',
      icon: UserPlus,
      href: '/beneficiaries/register',
      roles: ['reception_staff', 'case_manager', 'system_admin']
    },
    {
      title: t('search_beneficiaries'),
      description: 'Find and manage existing beneficiaries',
      icon: Search,
      href: '/beneficiaries',
      roles: ['reception_staff', 'case_manager', 'system_admin', 'finance_manager']
    },
    {
      title: t('generate_report'),
      description: 'Create distribution and analytics reports',
      icon: FileText,
      href: '/reports',
      roles: ['case_manager', 'system_admin', 'finance_manager']
    },
    {
      title: t('manage_distributions'),
      description: 'Configure distribution categories and amounts',
      icon: CreditCard,
      href: '/distributions',
      roles: ['system_admin', 'finance_manager']
    },
    {
      title: 'User Management',
      description: 'Manage system users and permissions',
      icon: Users,
      href: '/admin/users',
      roles: ['system_admin']
    },
    {
      title: 'System Settings',
      description: 'Configure system preferences',
      icon: Settings,
      href: '/admin/settings',
      roles: ['system_admin']
    }
  ]

  const userRole = session?.user?.role
  const filteredActions = actions.filter(action => 
    !action.roles || action.roles.includes(userRole as string)
  )

  return (
    <Card className="card-enhanced border-0 bg-card/80 backdrop-blur-sm">
      <CardHeader className="pb-4">
        <CardTitle className="text-xl font-semibold flex items-center space-x-2 rtl:space-x-reverse">
          <div className="h-8 w-8 rounded-lg bg-gradient-primary flex items-center justify-center">
            <Settings className="h-4 w-4 text-primary-foreground" />
          </div>
          <span>{t('quick_actions')}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredActions.map((action, index) => (
            <div
              key={index}
              className="group relative overflow-hidden rounded-xl border border-border/50 bg-card/50 p-4 transition-all duration-200 hover:shadow-medium hover:border-primary/20 hover:-translate-y-1 cursor-pointer"
              onClick={() => router.push(action.href)}
            >
              <div className="flex items-start space-x-3 rtl:space-x-reverse">
                <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                  <action.icon className="h-5 w-5 text-primary" />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-sm text-foreground group-hover:text-primary transition-colors">
                    {action.title}
                  </h3>
                  <p className="text-xs text-muted-foreground mt-1 leading-relaxed">
                    {action.description}
                  </p>
                </div>
              </div>
              <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}