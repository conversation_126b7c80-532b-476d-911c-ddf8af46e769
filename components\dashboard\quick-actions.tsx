'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useTranslation } from 'react-i18next'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { 
  UserPlus, 
  Search, 
  FileText, 
  Settings,
  Users,
  CreditCard
} from 'lucide-react'

export function QuickActions() {
  const { t } = useTranslation()
  const { data: session } = useSession()
  const router = useRouter()

  const actions = [
    {
      title: t('register_new_beneficiary'),
      description: 'Add a new beneficiary to the system',
      icon: UserPlus,
      href: '/beneficiaries/register',
      roles: ['reception_staff', 'case_manager', 'system_admin']
    },
    {
      title: t('search_beneficiaries'),
      description: 'Find and manage existing beneficiaries',
      icon: Search,
      href: '/beneficiaries',
      roles: ['reception_staff', 'case_manager', 'system_admin', 'finance_manager']
    },
    {
      title: t('generate_report'),
      description: 'Create distribution and analytics reports',
      icon: FileText,
      href: '/reports',
      roles: ['case_manager', 'system_admin', 'finance_manager']
    },
    {
      title: t('manage_distributions'),
      description: 'Configure distribution categories and amounts',
      icon: CreditCard,
      href: '/distributions',
      roles: ['system_admin', 'finance_manager']
    },
    {
      title: 'User Management',
      description: 'Manage system users and permissions',
      icon: Users,
      href: '/admin/users',
      roles: ['system_admin']
    },
    {
      title: 'System Settings',
      description: 'Configure system preferences',
      icon: Settings,
      href: '/admin/settings',
      roles: ['system_admin']
    }
  ]

  const userRole = session?.user?.role
  const filteredActions = actions.filter(action => 
    !action.roles || action.roles.includes(userRole as string)
  )

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('quick_actions')}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredActions.map((action, index) => (
            <Button
              key={index}
              variant="outline"
              className="h-auto p-4 flex flex-col items-start space-y-2"
              onClick={() => router.push(action.href)}
            >
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <action.icon className="h-4 w-4" />
                <span className="font-medium">{action.title}</span>
              </div>
              <span className="text-xs text-muted-foreground text-left rtl:text-right">
                {action.description}
              </span>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}