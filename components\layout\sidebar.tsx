
'use client'

import { useSession } from 'next-auth/react'
import { useTranslation } from 'react-i18next'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import { 
  LayoutDashboard,
  FileText,
  Users,
  ClipboardList,
  BarChart3,
  Settings,
  User,
  Building,
  Shield,
  MessageSquare,
  Calendar,
  CreditCard
} from 'lucide-react'
import type { UserRole } from '@/lib/types'

interface SidebarItem {
  title: string
  href: string
  icon: any
  roles?: UserRole[]
}

const sidebarItems: SidebarItem[] = [
  {
    title: 'dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: 'profile',
    href: '/profile',
    icon: User,
    roles: ['zakat_applicant'],
  },
  {
    title: 'requests',
    href: '/requests',
    icon: FileText,
  },
  {
    title: 'beneficiaries',
    href: '/beneficiaries',
    icon: Users,
    roles: ['reception_staff', 'researcher', 'department_head', 'admin_manager', 'minister', 'system_admin'],
  },
  {
    title: 'tasks',
    href: '/tasks',
    icon: ClipboardList,
    roles: ['reception_staff', 'researcher', 'banking_expert', 'department_head', 'admin_manager', 'minister'],
  },
  {
    title: 'reports',
    href: '/reports',
    icon: BarChart3,
    roles: ['reception_staff', 'researcher', 'banking_expert', 'department_head', 'admin_manager', 'minister', 'system_admin'],
  },
  {
    title: 'User Management',
    href: '/admin/users',
    icon: Users,
    roles: ['system_admin'],
  },
  {
    title: 'Assistance Types',
    href: '/admin/assistance-types',
    icon: CreditCard,
    roles: ['system_admin'],
  },
  {
    title: 'System Settings',
    href: '/admin/settings',
    icon: Settings,
    roles: ['system_admin'],
  },
]

export function Sidebar() {
  const { data: session } = useSession() || {}
  const { t } = useTranslation()
  const pathname = usePathname()

  if (!session?.user) {
    return null
  }

  const filteredItems = sidebarItems.filter(item => 
    !item.roles || item.roles.includes(session.user.role)
  )

  return (
    <div className="flex h-screen w-64 flex-col border-r border-border/40 bg-card/50 backdrop-blur-sm">
      <div className="flex-1 space-y-2 p-4">
        <div className="space-y-1">
          {filteredItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                'flex items-center gap-3 rounded-xl px-4 py-3 text-sm font-medium transition-all duration-200 group',
                pathname === item.href
                  ? 'bg-gradient-primary text-primary-foreground shadow-soft'
                  : 'text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground hover:shadow-soft'
              )}
            >
              <item.icon className={cn(
                "h-4 w-4 transition-transform duration-200",
                pathname === item.href ? "scale-110" : "group-hover:scale-105"
              )} />
              <span className="font-medium">{t(item.title)}</span>
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
}
