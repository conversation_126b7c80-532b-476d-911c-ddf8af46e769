'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { LucideIcon } from 'lucide-react'
import { useTranslation } from 'react-i18next'

interface SummaryWidgetProps {
  title: string
  value: string | number
  change?: {
    value: number
    type: 'increase' | 'decrease'
    period: string
  }
  icon: LucideIcon
  color?: 'default' | 'success' | 'warning' | 'destructive'
}

export function SummaryWidget({ 
  title, 
  value, 
  change, 
  icon: Icon, 
  color = 'default' 
}: SummaryWidgetProps) {
  const { t } = useTranslation()

  const colorClasses = {
    default: 'text-primary',
    success: 'text-green-600',
    warning: 'text-yellow-600',
    destructive: 'text-red-600'
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className={`h-4 w-4 ${colorClasses[color]}`} />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {change && (
          <div className="flex items-center space-x-2 rtl:space-x-reverse text-xs text-muted-foreground">
            <Badge 
              variant={change.type === 'increase' ? 'default' : 'secondary'}
              className="text-xs"
            >
              {change.type === 'increase' ? '+' : '-'}{Math.abs(change.value)}%
            </Badge>
            <span>
              {t(change.type)} {t('from')} {change.period}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}