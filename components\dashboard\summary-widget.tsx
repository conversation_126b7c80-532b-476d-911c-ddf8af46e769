'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { LucideIcon } from 'lucide-react'
import { useTranslation } from 'react-i18next'

interface SummaryWidgetProps {
  title: string
  value: string | number
  change?: {
    value: number
    type: 'increase' | 'decrease'
    period: string
  }
  icon: LucideIcon
  color?: 'default' | 'success' | 'warning' | 'destructive'
}

export function SummaryWidget({ 
  title, 
  value, 
  change, 
  icon: Icon, 
  color = 'default' 
}: SummaryWidgetProps) {
  const { t } = useTranslation()

  const colorClasses = {
    default: 'text-primary',
    success: 'text-success',
    warning: 'text-warning',
    destructive: 'text-destructive'
  }

  const backgroundClasses = {
    default: 'bg-primary/10',
    success: 'bg-success/10',
    warning: 'bg-warning/10',
    destructive: 'bg-destructive/10'
  }

  return (
    <Card className="card-enhanced border-0 bg-card/80 backdrop-blur-sm">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
        <CardTitle className="text-sm font-medium text-muted-foreground">{title}</CardTitle>
        <div className={`h-10 w-10 rounded-full ${backgroundClasses[color]} flex items-center justify-center`}>
          <Icon className={`h-5 w-5 ${colorClasses[color]}`} />
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="text-3xl font-bold tracking-tight mb-2">{value}</div>
        {change && (
          <div className="flex items-center space-x-2 rtl:space-x-reverse text-xs">
            <Badge
              variant={change.type === 'increase' ? 'default' : 'secondary'}
              className={`text-xs font-medium ${
                change.type === 'increase'
                  ? 'bg-success/20 text-success border-success/30'
                  : 'bg-muted text-muted-foreground'
              }`}
            >
              {change.type === 'increase' ? '↗' : '↘'} {Math.abs(change.value)}%
            </Badge>
            <span className="text-muted-foreground">
              {t(change.type)} {t('from')} {t(change.period)}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}